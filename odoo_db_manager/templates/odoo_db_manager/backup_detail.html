{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
{% endblock %}

{% block content %}
<div class="odoo-dashboard">
    <!-- شريط الأدوات العلوي -->
    <div class="odoo-toolbar">
        <div class="odoo-toolbar-left">
            <h1>{{ backup.name }}</h1>
        </div>
        <div class="odoo-toolbar-right">
            <a href="{% url 'odoo_db_manager:dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
            <a href="{% url 'odoo_db_manager:backup_download' backup.id %}"
               class="btn btn-success download-btn"
               data-filename="{{ backup.name }}_{{ backup.created_at|date:'Ymd_Hi' }}.gz">
                <i class="fas fa-download"></i> تحميل (.gz)
            </a>
            <a href="{% url 'odoo_db_manager:backup_restore' backup.id %}" class="btn btn-primary">
                <i class="fas fa-undo"></i> استعادة
            </a>
            <a href="{% url 'odoo_db_manager:backup_delete' backup.id %}" class="btn btn-danger">
                <i class="fas fa-trash"></i> حذف
            </a>
        </div>
    </div>

    <!-- تفاصيل النسخة الاحتياطية -->
    <div class="odoo-detail">
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">معلومات النسخة الاحتياطية</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tr>
                                <th>الاسم</th>
                                <td>{{ backup.name }}</td>
                            </tr>
                            <tr>
                                <th>قاعدة البيانات</th>
                                <td>
                                    <a href="{% url 'odoo_db_manager:database_detail' backup.database.id %}"></a>
                                        {{ backup.database.name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th>الحجم</th>
                                <td>
                                    {{ backup.size_display }}
                                    {% if backup.is_compressed %}
                                        <span class="badge badge-success ml-2">
                                            <i class="fas fa-compress-alt"></i> مضغوط
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>تاريخ الإنشاء</th>
                                <td>{{ backup.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>تم الإنشاء بواسطة</th>
                                <td>{{ backup.created_by.username|default:"" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">معلومات الملف</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tr>
                                <th>مسار الملف</th>
                                <td><code>{{ backup.file_path }}</code></td>
                            </tr>
                            <tr>
                                <th>نوع النسخة الاحتياطية</th>
                                <td>
                                    {% if backup.backup_type == 'customers' %}
                                        بيانات العملاء
                                    {% elif backup.backup_type == 'users' %}
                                        بيانات المستخدمين
                                    {% elif backup.backup_type == 'settings' %}
                                        إعدادات النظام
                                    {% elif backup.backup_type == 'full' %}
                                        كل البيانات
                                    {% else %}
                                        {{ backup.backup_type }}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>نوع الملف</th>
                                <td>
                                    <span class="badge {% if backup.is_compressed %}badge-success{% else %}badge-warning{% endif %}">
                                        <i class="fas {% if backup.is_compressed %}fa-file-archive{% else %}fa-file{% endif %}"></i>
                                        {{ backup.file_type_display }}
                                    </span>
                                    <small class="text-muted d-block">
                                        {{ backup.compression_info }}
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <th>تنسيق التحميل</th>
                                <td>
                                    <span class="badge badge-info">
                                        <i class="fas fa-download"></i>
                                        {% if backup.is_compressed %}
                                            تحميل مباشر (ملف مضغوط)
                                        {% else %}
                                            سيتم ضغطه عند التحميل (.gz)
                                        {% endif %}
                                    </span>
                                    <small class="text-muted d-block">
                                        {% if backup.is_compressed %}
                                            الملف مضغوط مسبقاً ويوفر مساحة التخزين
                                        {% else %}
                                            سيتم ضغط الملف تلقائياً عند التحميل لتوفير مساحة التخزين
                                        {% endif %}
                                    </small>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/download-helper.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين زر التحميل المحدد
    const downloadBtn = document.querySelector('.download-btn');

    if (downloadBtn) {
        downloadBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // الحصول على معلومات الملف
            const url = this.href;
            const filename = this.getAttribute('data-filename');

            // تغيير نص الزر
            const originalHTML = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
            this.style.pointerEvents = 'none';

            // بدء التحميل باستخدام المساعد المتقدم
            window.downloadHelper.downloadFile(url, filename)
                .then(success => {
                    if (success) {
                        // إظهار رسالة نجاح
                        const successMsg = document.createElement('div');
                        successMsg.innerHTML = `
                            <div style="
                                position: fixed; top: 20px; right: 20px;
                                background: #28a745; color: white;
                                padding: 10px 20px; border-radius: 5px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                                z-index: 10000; font-size: 14px;
                            ">
                                <i class="fas fa-check"></i> تم تحميل الملف بنجاح
                            </div>
                        `;
                        document.body.appendChild(successMsg);
                        setTimeout(() => successMsg.remove(), 3000);
                    }
                })
                .finally(() => {
                    // إعادة تعيين الزر
                    setTimeout(() => {
                        this.innerHTML = originalHTML;
                        this.style.pointerEvents = 'auto';
                    }, 1000);
                });

            return false;
        });

        console.log('✅ تم تحسين زر التحميل للملف:', downloadBtn.getAttribute('data-filename'));
    }
});
</script>
{% endblock %}
