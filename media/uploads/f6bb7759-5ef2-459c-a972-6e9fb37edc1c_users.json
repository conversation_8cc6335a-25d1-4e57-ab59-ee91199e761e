[{"model": "customers.customercategory", "pk": 1, "fields": {"name": "T.D.S", "description": "عميل تعارف معرض TDS شهر 5 سنة 2025", "created_at": "2025-06-03T16:26:04.909Z"}}, {"model": "customers.customercategory", "pk": 2, "fields": {"name": "Le <PERSON>", "description": "عميل تعارف <PERSON>", "created_at": "2025-06-03T16:27:16.505Z"}}, {"model": "customers.customercategory", "pk": 3, "fields": {"name": "ONLINE", "description": "عميل تعارف  سوشال ميديا", "created_at": "2025-06-03T16:27:47.544Z"}}, {"model": "customers.customercategory", "pk": 4, "fields": {"name": "فرع", "description": "عميل تعارف فرع", "created_at": "2025-06-03T16:35:49.206Z"}}, {"model": "customers.customercategory", "pk": 5, "fields": {"name": "مخزن رئيسي", "description": "تعار<PERSON> ج<PERSON>ة", "created_at": "2025-06-03T16:36:22.038Z"}}, {"model": "customers.customertype", "pk": 1, "fields": {"code": "retail", "name": "أف<PERSON><PERSON>", "description": "عملاء التجزئة", "is_active": true, "created_at": "2025-06-03T16:37:47.959Z"}}, {"model": "customers.customertype", "pk": 2, "fields": {"code": "wholesale", "name": "جم<PERSON>ة", "description": "عملاء الجملة", "is_active": true, "created_at": "2025-06-03T16:37:47.962Z"}}, {"model": "customers.customertype", "pk": 3, "fields": {"code": "corporate", "name": "شركات", "description": "العملاء من الشركات", "is_active": true, "created_at": "2025-06-03T16:37:47.964Z"}}, {"model": "customers.customertype", "pk": 4, "fields": {"code": "vip", "name": "VIP", "description": "عملاء VIP وكبار العملاء", "is_active": true, "created_at": "2025-06-03T16:37:47.966Z"}}, {"model": "customers.customertype", "pk": 5, "fields": {"code": "distributor", "name": "موزعين", "description": "الموزعين والوكلاء", "is_active": true, "created_at": "2025-06-03T16:37:47.968Z"}}, {"model": "customers.customertype", "pk": 6, "fields": {"code": "government", "name": "جهات حكومية", "description": "المؤسسات والجهات الحكومية", "is_active": true, "created_at": "2025-06-03T16:37:47.971Z"}}, {"model": "customers.customertype", "pk": 7, "fields": {"code": "designer", "name": "مهندس ديكور", "description": "", "is_active": true, "created_at": "2025-06-17T10:41:59.171Z"}}, {"model": "inventory.product", "pk": 695, "fields": {"name": "ART-114", "code": "891", "price": "440.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.293Z", "updated_at": "2025-05-13T17:42:02.293Z"}}, {"model": "inventory.product", "pk": 696, "fields": {"name": "ART-112", "code": "890", "price": "440.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.288Z", "updated_at": "2025-05-13T17:42:02.288Z"}}, {"model": "inventory.product", "pk": 697, "fields": {"name": "AMANDA-1", "code": "892", "price": "430.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.282Z", "updated_at": "2025-05-13T17:42:02.282Z"}}, {"model": "inventory.product", "pk": 698, "fields": {"name": "ARABIANO-1", "code": "893", "price": "305.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.277Z", "updated_at": "2025-05-13T17:42:02.277Z"}}, {"model": "inventory.product", "pk": 699, "fields": {"name": "AH-SHD", "code": "894", "price": "1090.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.272Z", "updated_at": "2025-05-13T17:42:02.272Z"}}, {"model": "inventory.product", "pk": 700, "fields": {"name": "ATX", "code": "895", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.265Z", "updated_at": "2025-05-13T17:42:02.265Z"}}, {"model": "inventory.product", "pk": 701, "fields": {"name": "AVA-21", "code": "896", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.260Z", "updated_at": "2025-05-13T17:42:02.260Z"}}, {"model": "inventory.product", "pk": 702, "fields": {"name": "BERO", "code": "897", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.255Z", "updated_at": "2025-05-13T17:42:02.255Z"}}, {"model": "inventory.product", "pk": 703, "fields": {"name": "BICA", "code": "898", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.249Z", "updated_at": "2025-05-13T17:42:02.249Z"}}, {"model": "inventory.product", "pk": 704, "fields": {"name": "BLACKOUT-A-K", "code": "899", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.244Z", "updated_at": "2025-05-13T17:42:02.244Z"}}, {"model": "inventory.product", "pk": 705, "fields": {"name": "BLACKOUT-G", "code": "900", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.239Z", "updated_at": "2025-05-13T17:42:02.239Z"}}, {"model": "inventory.product", "pk": 706, "fields": {"name": "BLACKOUT-LINEN", "code": "901", "price": "750.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.232Z", "updated_at": "2025-05-13T17:42:02.232Z"}}, {"model": "inventory.product", "pk": 707, "fields": {"name": "BLACKOUT-MORE", "code": "902", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.227Z", "updated_at": "2025-05-13T17:42:02.227Z"}}, {"model": "inventory.product", "pk": 708, "fields": {"name": "BMT-MISS", "code": "903", "price": "365.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.222Z", "updated_at": "2025-05-13T17:42:02.222Z"}}, {"model": "inventory.product", "pk": 709, "fields": {"name": "CAREN", "code": "904", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.216Z", "updated_at": "2025-05-13T17:42:02.216Z"}}, {"model": "inventory.product", "pk": 710, "fields": {"name": "CAREN-2", "code": "905", "price": "345.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.211Z", "updated_at": "2025-05-13T17:42:02.211Z"}}, {"model": "inventory.product", "pk": 711, "fields": {"name": "CARNIVAL-1", "code": "906", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.206Z", "updated_at": "2025-05-13T17:42:02.206Z"}}, {"model": "inventory.product", "pk": 712, "fields": {"name": "CLASH-1", "code": "907", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.200Z", "updated_at": "2025-05-13T17:42:02.200Z"}}, {"model": "inventory.product", "pk": 713, "fields": {"name": "CLASSICO-1", "code": "908", "price": "250.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.195Z", "updated_at": "2025-05-13T17:42:02.195Z"}}, {"model": "inventory.product", "pk": 714, "fields": {"name": "COIN-1", "code": "909", "price": "330.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.190Z", "updated_at": "2025-05-13T17:42:02.190Z"}}, {"model": "inventory.product", "pk": 715, "fields": {"name": "COPRA", "code": "910", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.186Z", "updated_at": "2025-05-13T17:42:02.186Z"}}, {"model": "inventory.product", "pk": 716, "fields": {"name": "COSTA RICA-1", "code": "911", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.180Z", "updated_at": "2025-05-13T17:42:02.180Z"}}, {"model": "inventory.product", "pk": 717, "fields": {"name": "COSTA RICA-3", "code": "912", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.175Z", "updated_at": "2025-05-13T17:42:02.175Z"}}, {"model": "inventory.product", "pk": 718, "fields": {"name": "COSTA RICA-5", "code": "913", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.170Z", "updated_at": "2025-05-13T17:42:02.170Z"}}, {"model": "inventory.product", "pk": 719, "fields": {"name": "COSTA RICA-7", "code": "914", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.163Z", "updated_at": "2025-05-13T17:42:02.163Z"}}, {"model": "inventory.product", "pk": 720, "fields": {"name": "COVO", "code": "915", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.158Z", "updated_at": "2025-05-13T17:42:02.158Z"}}, {"model": "inventory.product", "pk": 721, "fields": {"name": "CRUZ", "code": "916", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.154Z", "updated_at": "2025-05-13T17:42:02.154Z"}}, {"model": "inventory.product", "pk": 722, "fields": {"name": "CUSTARD", "code": "917", "price": "470.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.147Z", "updated_at": "2025-05-13T17:42:02.147Z"}}, {"model": "inventory.product", "pk": 723, "fields": {"name": "DEJA", "code": "918", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.142Z", "updated_at": "2025-05-13T17:42:02.142Z"}}, {"model": "inventory.product", "pk": 724, "fields": {"name": "DIGITAL", "code": "919", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.137Z", "updated_at": "2025-05-13T17:42:02.137Z"}}, {"model": "inventory.product", "pk": 725, "fields": {"name": "DONA", "code": "920", "price": "105.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.131Z", "updated_at": "2025-05-13T17:42:02.131Z"}}, {"model": "inventory.product", "pk": 726, "fields": {"name": "DORA-1", "code": "921", "price": "345.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.126Z", "updated_at": "2025-05-13T17:42:02.126Z"}}, {"model": "inventory.product", "pk": 727, "fields": {"name": "DOTS", "code": "922", "price": "210.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.121Z", "updated_at": "2025-05-13T17:42:02.121Z"}}, {"model": "inventory.product", "pk": 728, "fields": {"name": "ELANTRA-1", "code": "923", "price": "330.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.115Z", "updated_at": "2025-05-13T17:42:02.115Z"}}, {"model": "inventory.product", "pk": 729, "fields": {"name": "FERRARI", "code": "924", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.110Z", "updated_at": "2025-05-13T17:42:02.110Z"}}, {"model": "inventory.product", "pk": 730, "fields": {"name": "FSZ", "code": "925", "price": "430.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.105Z", "updated_at": "2025-05-13T17:42:02.105Z"}}, {"model": "inventory.product", "pk": 731, "fields": {"name": "FORD", "code": "926", "price": "375.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.098Z", "updated_at": "2025-05-13T17:42:02.098Z"}}, {"model": "inventory.product", "pk": 732, "fields": {"name": "FUR", "code": "927", "price": "765.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.094Z", "updated_at": "2025-05-13T17:42:02.094Z"}}, {"model": "inventory.product", "pk": 733, "fields": {"name": "GERMAN-2WIDTH", "code": "928", "price": "395.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.089Z", "updated_at": "2025-05-13T17:42:02.089Z"}}, {"model": "inventory.product", "pk": 734, "fields": {"name": "GOLDINO-2", "code": "929", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.083Z", "updated_at": "2025-05-13T17:42:02.083Z"}}, {"model": "inventory.product", "pk": 735, "fields": {"name": "GOLF-1", "code": "930", "price": "430.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.078Z", "updated_at": "2025-05-13T17:42:02.078Z"}}, {"model": "inventory.product", "pk": 736, "fields": {"name": "GRAND", "code": "931", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.073Z", "updated_at": "2025-05-13T17:42:02.073Z"}}, {"model": "inventory.product", "pk": 737, "fields": {"name": "HIDRA-1", "code": "932", "price": "325.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.067Z", "updated_at": "2025-05-13T17:42:02.067Z"}}, {"model": "inventory.product", "pk": 738, "fields": {"name": "INFINITE", "code": "933", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.062Z", "updated_at": "2025-05-13T17:42:02.062Z"}}, {"model": "inventory.product", "pk": 739, "fields": {"name": "INFINITE-2", "code": "934", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.057Z", "updated_at": "2025-05-13T17:42:02.058Z"}}, {"model": "inventory.product", "pk": 740, "fields": {"name": "JOKER-1", "code": "935", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.053Z", "updated_at": "2025-05-13T17:42:02.053Z"}}, {"model": "inventory.product", "pk": 741, "fields": {"name": "JULIET", "code": "936", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.047Z", "updated_at": "2025-05-13T17:42:02.047Z"}}, {"model": "inventory.product", "pk": 742, "fields": {"name": "KANDY", "code": "937", "price": "250.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.043Z", "updated_at": "2025-05-13T17:42:02.043Z"}}, {"model": "inventory.product", "pk": 743, "fields": {"name": "KENDA-1", "code": "938", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.038Z", "updated_at": "2025-05-13T17:42:02.038Z"}}, {"model": "inventory.product", "pk": 744, "fields": {"name": "KOYA", "code": "939", "price": "110.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.032Z", "updated_at": "2025-05-13T17:42:02.032Z"}}, {"model": "inventory.product", "pk": 745, "fields": {"name": "LA POIRE-1", "code": "940", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.028Z", "updated_at": "2025-05-13T17:42:02.028Z"}}, {"model": "inventory.product", "pk": 746, "fields": {"name": "LEXUS", "code": "941", "price": "210.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.023Z", "updated_at": "2025-05-13T17:42:02.023Z"}}, {"model": "inventory.product", "pk": 747, "fields": {"name": "LIPNO", "code": "942", "price": "575.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.017Z", "updated_at": "2025-05-13T17:42:02.017Z"}}, {"model": "inventory.product", "pk": 748, "fields": {"name": "LONDON", "code": "943", "price": "175.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.012Z", "updated_at": "2025-05-13T17:42:02.012Z"}}, {"model": "inventory.product", "pk": 749, "fields": {"name": "LOLI POP-1", "code": "944", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.008Z", "updated_at": "2025-05-13T17:42:02.008Z"}}, {"model": "inventory.product", "pk": 750, "fields": {"name": "MM-K1", "code": "945", "price": "380.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:02.003Z", "updated_at": "2025-05-13T17:42:02.003Z"}}, {"model": "inventory.product", "pk": 751, "fields": {"name": "MALO", "code": "946", "price": "420.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.997Z", "updated_at": "2025-05-13T17:42:01.997Z"}}, {"model": "inventory.product", "pk": 752, "fields": {"name": "MARSEILLE", "code": "947", "price": "750.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.993Z", "updated_at": "2025-05-13T17:42:01.993Z"}}, {"model": "inventory.product", "pk": 753, "fields": {"name": "MASHA", "code": "948", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.988Z", "updated_at": "2025-05-13T17:42:01.988Z"}}, {"model": "inventory.product", "pk": 754, "fields": {"name": "MOHER-1WIDTH", "code": "949", "price": "150.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.982Z", "updated_at": "2025-05-13T17:42:01.982Z"}}, {"model": "inventory.product", "pk": 755, "fields": {"name": "MOHER-GERMAN-1WIDTH", "code": "950", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.977Z", "updated_at": "2025-05-13T17:42:01.977Z"}}, {"model": "inventory.product", "pk": 756, "fields": {"name": "MOHER-SALAH", "code": "951", "price": "395.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.973Z", "updated_at": "2025-05-13T17:42:01.973Z"}}, {"model": "inventory.product", "pk": 757, "fields": {"name": "MONTREAL", "code": "952", "price": "210.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.966Z", "updated_at": "2025-05-13T17:42:01.966Z"}}, {"model": "inventory.product", "pk": 758, "fields": {"name": "MOTTO", "code": "953", "price": "300.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.962Z", "updated_at": "2025-05-13T17:42:01.962Z"}}, {"model": "inventory.product", "pk": 759, "fields": {"name": "NADEKO-5", "code": "954", "price": "345.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.958Z", "updated_at": "2025-05-13T17:42:01.958Z"}}, {"model": "inventory.product", "pk": 760, "fields": {"name": "NEMO", "code": "955", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.953Z", "updated_at": "2025-05-13T17:42:01.953Z"}}, {"model": "inventory.product", "pk": 761, "fields": {"name": "OKAA", "code": "956", "price": "375.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.946Z", "updated_at": "2025-05-13T17:42:01.947Z"}}, {"model": "inventory.product", "pk": 762, "fields": {"name": "ONTARIO-2", "code": "957", "price": "210.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.942Z", "updated_at": "2025-05-13T17:42:01.942Z"}}, {"model": "inventory.product", "pk": 763, "fields": {"name": "ORIOS", "code": "958", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.935Z", "updated_at": "2025-05-13T17:42:01.935Z"}}, {"model": "inventory.product", "pk": 764, "fields": {"name": "PALO", "code": "959", "price": "245.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.927Z", "updated_at": "2025-05-13T17:42:01.927Z"}}, {"model": "inventory.product", "pk": 765, "fields": {"name": "PARADISE-1", "code": "960", "price": "245.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.921Z", "updated_at": "2025-05-13T17:42:01.921Z"}}, {"model": "inventory.product", "pk": 766, "fields": {"name": "PHOENIX-1", "code": "961", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.913Z", "updated_at": "2025-05-13T17:42:01.913Z"}}, {"model": "inventory.product", "pk": 767, "fields": {"name": "POLUVARD", "code": "962", "price": "350.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.907Z", "updated_at": "2025-05-13T17:42:01.907Z"}}, {"model": "inventory.product", "pk": 768, "fields": {"name": "PORTO", "code": "963", "price": "750.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.898Z", "updated_at": "2025-05-13T17:42:01.898Z"}}, {"model": "inventory.product", "pk": 769, "fields": {"name": "PRINT MOH", "code": "964", "price": "350.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.892Z", "updated_at": "2025-05-13T17:42:01.892Z"}}, {"model": "inventory.product", "pk": 770, "fields": {"name": "PRONTO-1", "code": "965", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.883Z", "updated_at": "2025-05-13T17:42:01.883Z"}}, {"model": "inventory.product", "pk": 771, "fields": {"name": "RANG", "code": "966", "price": "310.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.876Z", "updated_at": "2025-05-13T17:42:01.876Z"}}, {"model": "inventory.product", "pk": 772, "fields": {"name": "RENDA-BH", "code": "967", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.869Z", "updated_at": "2025-05-13T17:42:01.869Z"}}, {"model": "inventory.product", "pk": 773, "fields": {"name": "ROLEX-BH", "code": "968", "price": "245.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.861Z", "updated_at": "2025-05-13T17:42:01.861Z"}}, {"model": "inventory.product", "pk": 774, "fields": {"name": "ROSE-1", "code": "969", "price": "345.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.854Z", "updated_at": "2025-05-13T17:42:01.854Z"}}, {"model": "inventory.product", "pk": 775, "fields": {"name": "RUYAN", "code": "970", "price": "325.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.846Z", "updated_at": "2025-05-13T17:42:01.846Z"}}, {"model": "inventory.product", "pk": 776, "fields": {"name": "SANTA-J", "code": "971", "price": "470.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.840Z", "updated_at": "2025-05-13T17:42:01.840Z"}}, {"model": "inventory.product", "pk": 777, "fields": {"name": "SH-ARABIC", "code": "972", "price": "340.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.832Z", "updated_at": "2025-05-13T17:42:01.832Z"}}, {"model": "inventory.product", "pk": 778, "fields": {"name": "SH-ARABIC-2", "code": "973", "price": "340.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.823Z", "updated_at": "2025-05-13T17:42:01.823Z"}}, {"model": "inventory.product", "pk": 779, "fields": {"name": "SH-ARABISC-1", "code": "974", "price": "340.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.815Z", "updated_at": "2025-05-13T17:42:01.815Z"}}, {"model": "inventory.product", "pk": 780, "fields": {"name": "SIDRA", "code": "975", "price": "325.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.810Z", "updated_at": "2025-05-13T17:42:01.810Z"}}, {"model": "inventory.product", "pk": 781, "fields": {"name": "SIDRA-2", "code": "976", "price": "325.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.806Z", "updated_at": "2025-05-13T17:42:01.806Z"}}, {"model": "inventory.product", "pk": 782, "fields": {"name": "SILK-1WIDTH", "code": "977", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.800Z", "updated_at": "2025-05-13T17:42:01.800Z"}}, {"model": "inventory.product", "pk": 783, "fields": {"name": "SILK-M", "code": "978", "price": "345.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.795Z", "updated_at": "2025-05-13T17:42:01.795Z"}}, {"model": "inventory.product", "pk": 784, "fields": {"name": "SOFA", "code": "979", "price": "455.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.790Z", "updated_at": "2025-05-13T17:42:01.790Z"}}, {"model": "inventory.product", "pk": 785, "fields": {"name": "SOHO", "code": "980", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.784Z", "updated_at": "2025-05-13T17:42:01.785Z"}}, {"model": "inventory.product", "pk": 786, "fields": {"name": "SOIREE-2", "code": "981", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.779Z", "updated_at": "2025-05-13T17:42:01.779Z"}}, {"model": "inventory.product", "pk": 787, "fields": {"name": "SOIREE-4", "code": "982", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.774Z", "updated_at": "2025-05-13T17:42:01.774Z"}}, {"model": "inventory.product", "pk": 788, "fields": {"name": "SOIREE-6", "code": "983", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.768Z", "updated_at": "2025-05-13T17:42:01.768Z"}}, {"model": "inventory.product", "pk": 789, "fields": {"name": "SPANISH-2", "code": "984", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.762Z", "updated_at": "2025-05-13T17:42:01.762Z"}}, {"model": "inventory.product", "pk": 790, "fields": {"name": "SPEED", "code": "985", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.757Z", "updated_at": "2025-05-13T17:42:01.757Z"}}, {"model": "inventory.product", "pk": 791, "fields": {"name": "STELLA", "code": "986", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.752Z", "updated_at": "2025-05-13T17:42:01.752Z"}}, {"model": "inventory.product", "pk": 792, "fields": {"name": "TED", "code": "987", "price": "210.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.746Z", "updated_at": "2025-05-13T17:42:01.746Z"}}, {"model": "inventory.product", "pk": 793, "fields": {"name": "TRE-1WIDTH-1", "code": "988", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.742Z", "updated_at": "2025-05-13T17:42:01.742Z"}}, {"model": "inventory.product", "pk": 794, "fields": {"name": "TRE-1WIDTH-3", "code": "989", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.737Z", "updated_at": "2025-05-13T17:42:01.737Z"}}, {"model": "inventory.product", "pk": 795, "fields": {"name": "TRE-1WIDTH-5", "code": "990", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.731Z", "updated_at": "2025-05-13T17:42:01.731Z"}}, {"model": "inventory.product", "pk": 796, "fields": {"name": "TRE-1WIDTH-7", "code": "991", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.726Z", "updated_at": "2025-05-13T17:42:01.726Z"}}, {"model": "inventory.product", "pk": 797, "fields": {"name": "TREASURE-2WIDTH", "code": "992", "price": "310.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.721Z", "updated_at": "2025-05-13T17:42:01.721Z"}}, {"model": "inventory.product", "pk": 798, "fields": {"name": "TOKYO", "code": "993", "price": "210.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.715Z", "updated_at": "2025-05-13T17:42:01.715Z"}}, {"model": "inventory.product", "pk": 799, "fields": {"name": "VASE-1", "code": "994", "price": "330.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.710Z", "updated_at": "2025-05-13T17:42:01.710Z"}}, {"model": "inventory.product", "pk": 800, "fields": {"name": "VELVET-11", "code": "995", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.705Z", "updated_at": "2025-05-13T17:42:01.705Z"}}, {"model": "inventory.product", "pk": 801, "fields": {"name": "VINTINO", "code": "996", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.698Z", "updated_at": "2025-05-13T17:42:01.698Z"}}, {"model": "inventory.product", "pk": 802, "fields": {"name": "WENDY", "code": "997", "price": "330.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.693Z", "updated_at": "2025-05-13T17:42:01.693Z"}}, {"model": "inventory.product", "pk": 803, "fields": {"name": "Z", "code": "998", "price": "305.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.688Z", "updated_at": "2025-05-13T17:42:01.688Z"}}, {"model": "inventory.product", "pk": 804, "fields": {"name": "ZARA", "code": "999", "price": "320.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.681Z", "updated_at": "2025-05-13T17:42:01.681Z"}}, {"model": "inventory.product", "pk": 805, "fields": {"name": "601-F", "code": "1000", "price": "350.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.677Z", "updated_at": "2025-05-13T17:42:01.677Z"}}, {"model": "inventory.product", "pk": 806, "fields": {"name": "A-2", "code": "1001", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.672Z", "updated_at": "2025-05-13T17:42:01.672Z"}}, {"model": "inventory.product", "pk": 807, "fields": {"name": "AH-43129", "code": "1002", "price": "1195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.666Z", "updated_at": "2025-05-13T17:42:01.666Z"}}, {"model": "inventory.product", "pk": 808, "fields": {"name": "ALEX-1", "code": "1003", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.661Z", "updated_at": "2025-05-13T17:42:01.661Z"}}, {"model": "inventory.product", "pk": 809, "fields": {"name": "ALEX-11", "code": "1004", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.656Z", "updated_at": "2025-05-13T17:42:01.656Z"}}, {"model": "inventory.product", "pk": 810, "fields": {"name": "ALEX-13", "code": "1005", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.650Z", "updated_at": "2025-05-13T17:42:01.650Z"}}, {"model": "inventory.product", "pk": 811, "fields": {"name": "ALEX-15", "code": "1006", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.645Z", "updated_at": "2025-05-13T17:42:01.645Z"}}, {"model": "inventory.product", "pk": 812, "fields": {"name": "ALEX-17", "code": "1007", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.640Z", "updated_at": "2025-05-13T17:42:01.640Z"}}, {"model": "inventory.product", "pk": 813, "fields": {"name": "ALEX-19", "code": "1008", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.634Z", "updated_at": "2025-05-13T17:42:01.634Z"}}, {"model": "inventory.product", "pk": 814, "fields": {"name": "ALEX-20", "code": "1009", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.628Z", "updated_at": "2025-05-13T17:42:01.628Z"}}, {"model": "inventory.product", "pk": 815, "fields": {"name": "ALEX-22", "code": "1010", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.623Z", "updated_at": "2025-05-13T17:42:01.623Z"}}, {"model": "inventory.product", "pk": 816, "fields": {"name": "ALEX-24", "code": "1011", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.617Z", "updated_at": "2025-05-13T17:42:01.617Z"}}, {"model": "inventory.product", "pk": 817, "fields": {"name": "ALEX-26", "code": "1012", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.612Z", "updated_at": "2025-05-13T17:42:01.612Z"}}, {"model": "inventory.product", "pk": 818, "fields": {"name": "ALEX-28", "code": "1013", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.607Z", "updated_at": "2025-05-13T17:42:01.607Z"}}, {"model": "inventory.product", "pk": 819, "fields": {"name": "ALEX-3", "code": "1014", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.603Z", "updated_at": "2025-05-13T17:42:01.603Z"}}, {"model": "inventory.product", "pk": 820, "fields": {"name": "ALEX-31", "code": "1015", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.597Z", "updated_at": "2025-05-13T17:42:01.597Z"}}, {"model": "inventory.product", "pk": 821, "fields": {"name": "ALEX-33", "code": "1016", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.592Z", "updated_at": "2025-05-13T17:42:01.592Z"}}, {"model": "inventory.product", "pk": 822, "fields": {"name": "ALEX-35", "code": "1017", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.588Z", "updated_at": "2025-05-13T17:42:01.588Z"}}, {"model": "inventory.product", "pk": 823, "fields": {"name": "ALEX-4", "code": "1018", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.582Z", "updated_at": "2025-05-13T17:42:01.582Z"}}, {"model": "inventory.product", "pk": 824, "fields": {"name": "ALEX-6", "code": "1019", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.577Z", "updated_at": "2025-05-13T17:42:01.577Z"}}, {"model": "inventory.product", "pk": 825, "fields": {"name": "ALEX-8", "code": "1020", "price": "185.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.572Z", "updated_at": "2025-05-13T17:42:01.572Z"}}, {"model": "inventory.product", "pk": 826, "fields": {"name": "ALINE", "code": "1021", "price": "650.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.565Z", "updated_at": "2025-05-13T17:42:01.565Z"}}, {"model": "inventory.product", "pk": 827, "fields": {"name": "AMAZON", "code": "1022", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.560Z", "updated_at": "2025-05-13T17:42:01.560Z"}}, {"model": "inventory.product", "pk": 828, "fields": {"name": "BALDAMYNTA", "code": "1023", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.555Z", "updated_at": "2025-05-13T17:42:01.555Z"}}, {"model": "inventory.product", "pk": 829, "fields": {"name": "BAND-3397", "code": "1024", "price": "690.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.549Z", "updated_at": "2025-05-13T17:42:01.549Z"}}, {"model": "inventory.product", "pk": 830, "fields": {"name": "BAND-7296", "code": "1025", "price": "570.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.544Z", "updated_at": "2025-05-13T17:42:01.544Z"}}, {"model": "inventory.product", "pk": 831, "fields": {"name": "BARCHALONA", "code": "1026", "price": "755.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.539Z", "updated_at": "2025-05-13T17:42:01.539Z"}}, {"model": "inventory.product", "pk": 832, "fields": {"name": "CASABLANCA-1", "code": "1027", "price": "620.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.533Z", "updated_at": "2025-05-13T17:42:01.533Z"}}, {"model": "inventory.product", "pk": 833, "fields": {"name": "CHARLIE", "code": "1028", "price": "415.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.528Z", "updated_at": "2025-05-13T17:42:01.528Z"}}, {"model": "inventory.product", "pk": 834, "fields": {"name": "COLOMBIA", "code": "1029", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.523Z", "updated_at": "2025-05-13T17:42:01.523Z"}}, {"model": "inventory.product", "pk": 835, "fields": {"name": "DEMO TWINS-1", "code": "1030", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.517Z", "updated_at": "2025-05-13T17:42:01.517Z"}}, {"model": "inventory.product", "pk": 836, "fields": {"name": "DEMO-1964", "code": "1031", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.512Z", "updated_at": "2025-05-13T17:42:01.512Z"}}, {"model": "inventory.product", "pk": 837, "fields": {"name": "DEMO-2134", "code": "1032", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.507Z", "updated_at": "2025-05-13T17:42:01.507Z"}}, {"model": "inventory.product", "pk": 838, "fields": {"name": "DEMO-2426", "code": "1033", "price": "310.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.499Z", "updated_at": "2025-05-13T17:42:01.499Z"}}, {"model": "inventory.product", "pk": 839, "fields": {"name": "DEMO-2441", "code": "1034", "price": "245.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.494Z", "updated_at": "2025-05-13T17:42:01.494Z"}}, {"model": "inventory.product", "pk": 840, "fields": {"name": "DEMO-2678", "code": "1035", "price": "250.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.489Z", "updated_at": "2025-05-13T17:42:01.489Z"}}, {"model": "inventory.product", "pk": 841, "fields": {"name": "DEMO-LINEN", "code": "1036", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.483Z", "updated_at": "2025-05-13T17:42:01.483Z"}}, {"model": "inventory.product", "pk": 842, "fields": {"name": "DONUT", "code": "1037", "price": "1060.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.478Z", "updated_at": "2025-05-13T17:42:01.478Z"}}, {"model": "inventory.product", "pk": 843, "fields": {"name": "DOS-2001", "code": "1038", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.473Z", "updated_at": "2025-05-13T17:42:01.473Z"}}, {"model": "inventory.product", "pk": 844, "fields": {"name": "DOS-2003A", "code": "1039", "price": "560.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.467Z", "updated_at": "2025-05-13T17:42:01.467Z"}}, {"model": "inventory.product", "pk": 845, "fields": {"name": "DOS-225", "code": "1040", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.462Z", "updated_at": "2025-05-13T17:42:01.462Z"}}, {"model": "inventory.product", "pk": 846, "fields": {"name": "DOS-332", "code": "1041", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.457Z", "updated_at": "2025-05-13T17:42:01.457Z"}}, {"model": "inventory.product", "pk": 847, "fields": {"name": "DOS-4010", "code": "1042", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.450Z", "updated_at": "2025-05-13T17:42:01.450Z"}}, {"model": "inventory.product", "pk": 848, "fields": {"name": "DOS-5006A", "code": "1043", "price": "655.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.445Z", "updated_at": "2025-05-13T17:42:01.445Z"}}, {"model": "inventory.product", "pk": 849, "fields": {"name": "DOS-5011", "code": "1044", "price": "435.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.440Z", "updated_at": "2025-05-13T17:42:01.440Z"}}, {"model": "inventory.product", "pk": 850, "fields": {"name": "DOS-6001", "code": "1045", "price": "345.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.433Z", "updated_at": "2025-05-13T17:42:01.433Z"}}, {"model": "inventory.product", "pk": 851, "fields": {"name": "DOS-6023", "code": "1046", "price": "430.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.427Z", "updated_at": "2025-05-13T17:42:01.427Z"}}, {"model": "inventory.product", "pk": 852, "fields": {"name": "DOS-6072", "code": "1047", "price": "545.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.421Z", "updated_at": "2025-05-13T17:42:01.421Z"}}, {"model": "inventory.product", "pk": 853, "fields": {"name": "DOS-6075", "code": "1048", "price": "625.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.413Z", "updated_at": "2025-05-13T17:42:01.413Z"}}, {"model": "inventory.product", "pk": 854, "fields": {"name": "DOS-6092", "code": "1049", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.408Z", "updated_at": "2025-05-13T17:42:01.408Z"}}, {"model": "inventory.product", "pk": 855, "fields": {"name": "DOS-6092-C", "code": "1050", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.403Z", "updated_at": "2025-05-13T17:42:01.403Z"}}, {"model": "inventory.product", "pk": 856, "fields": {"name": "DOS-7040", "code": "1051", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.397Z", "updated_at": "2025-05-13T17:42:01.397Z"}}, {"model": "inventory.product", "pk": 857, "fields": {"name": "DOS-7055", "code": "1052", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.391Z", "updated_at": "2025-05-13T17:42:01.391Z"}}, {"model": "inventory.product", "pk": 858, "fields": {"name": "DOS-7066", "code": "1053", "price": "545.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.386Z", "updated_at": "2025-05-13T17:42:01.386Z"}}, {"model": "inventory.product", "pk": 859, "fields": {"name": "DOS-7089", "code": "1054", "price": "430.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.379Z", "updated_at": "2025-05-13T17:42:01.379Z"}}, {"model": "inventory.product", "pk": 860, "fields": {"name": "DOS-7115", "code": "1055", "price": "650.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.374Z", "updated_at": "2025-05-13T17:42:01.374Z"}}, {"model": "inventory.product", "pk": 861, "fields": {"name": "DOS-7137", "code": "1056", "price": "590.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.368Z", "updated_at": "2025-05-13T17:42:01.368Z"}}, {"model": "inventory.product", "pk": 862, "fields": {"name": "DOS-7153", "code": "1057", "price": "590.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.362Z", "updated_at": "2025-05-13T17:42:01.362Z"}}, {"model": "inventory.product", "pk": 863, "fields": {"name": "DOS-7155", "code": "1058", "price": "375.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.357Z", "updated_at": "2025-05-13T17:42:01.357Z"}}, {"model": "inventory.product", "pk": 864, "fields": {"name": "DOS-7209", "code": "1059", "price": "430.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.351Z", "updated_at": "2025-05-13T17:42:01.351Z"}}, {"model": "inventory.product", "pk": 865, "fields": {"name": "DOS-8068", "code": "1060", "price": "560.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.346Z", "updated_at": "2025-05-13T17:42:01.346Z"}}, {"model": "inventory.product", "pk": 866, "fields": {"name": "DOS-8089", "code": "1061", "price": "975.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.341Z", "updated_at": "2025-05-13T17:42:01.341Z"}}, {"model": "inventory.product", "pk": 867, "fields": {"name": "DOS-8394", "code": "1062", "price": "820.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.336Z", "updated_at": "2025-05-13T17:42:01.336Z"}}, {"model": "inventory.product", "pk": 868, "fields": {"name": "DOS-8569", "code": "1063", "price": "635.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.330Z", "updated_at": "2025-05-13T17:42:01.330Z"}}, {"model": "inventory.product", "pk": 869, "fields": {"name": "DOS-8588", "code": "1064", "price": "695.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.325Z", "updated_at": "2025-05-13T17:42:01.325Z"}}, {"model": "inventory.product", "pk": 870, "fields": {"name": "DOS-8595", "code": "1065", "price": "695.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.320Z", "updated_at": "2025-05-13T17:42:01.320Z"}}, {"model": "inventory.product", "pk": 871, "fields": {"name": "DOS-8603", "code": "1066", "price": "695.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.314Z", "updated_at": "2025-05-13T17:42:01.314Z"}}, {"model": "inventory.product", "pk": 872, "fields": {"name": "DOS-8721", "code": "1067", "price": "890.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.309Z", "updated_at": "2025-05-13T17:42:01.309Z"}}, {"model": "inventory.product", "pk": 873, "fields": {"name": "DOS-8729", "code": "1068", "price": "780.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.304Z", "updated_at": "2025-05-13T17:42:01.304Z"}}, {"model": "inventory.product", "pk": 874, "fields": {"name": "DOS-8752", "code": "1069", "price": "675.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.297Z", "updated_at": "2025-05-13T17:42:01.297Z"}}, {"model": "inventory.product", "pk": 875, "fields": {"name": "DOS-8869", "code": "1070", "price": "860.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.293Z", "updated_at": "2025-05-13T17:42:01.293Z"}}, {"model": "inventory.product", "pk": 876, "fields": {"name": "DOS-8917", "code": "1071", "price": "930.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.288Z", "updated_at": "2025-05-13T17:42:01.288Z"}}, {"model": "inventory.product", "pk": 877, "fields": {"name": "DOS-8946", "code": "1072", "price": "750.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.281Z", "updated_at": "2025-05-13T17:42:01.281Z"}}, {"model": "inventory.product", "pk": 878, "fields": {"name": "DOS-8988", "code": "1073", "price": "585.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.276Z", "updated_at": "2025-05-13T17:42:01.276Z"}}, {"model": "inventory.product", "pk": 879, "fields": {"name": "DOS-9034", "code": "1074", "price": "980.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.272Z", "updated_at": "2025-05-13T17:42:01.272Z"}}, {"model": "inventory.product", "pk": 880, "fields": {"name": "DOS-9045", "code": "1075", "price": "860.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.266Z", "updated_at": "2025-05-13T17:42:01.266Z"}}, {"model": "inventory.product", "pk": 881, "fields": {"name": "DOS-9048", "code": "1076", "price": "585.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.261Z", "updated_at": "2025-05-13T17:42:01.261Z"}}, {"model": "inventory.product", "pk": 882, "fields": {"name": "DOS-9125", "code": "1077", "price": "625.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.256Z", "updated_at": "2025-05-13T17:42:01.256Z"}}, {"model": "inventory.product", "pk": 883, "fields": {"name": "DOS-9143", "code": "1078", "price": "1020.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.249Z", "updated_at": "2025-05-13T17:42:01.249Z"}}, {"model": "inventory.product", "pk": 884, "fields": {"name": "DOS-9171", "code": "1079", "price": "545.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.245Z", "updated_at": "2025-05-13T17:42:01.245Z"}}, {"model": "inventory.product", "pk": 885, "fields": {"name": "DREAMS", "code": "1080", "price": "740.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.240Z", "updated_at": "2025-05-13T17:42:01.240Z"}}, {"model": "inventory.product", "pk": 886, "fields": {"name": "EURO-N2", "code": "1081", "price": "620.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.232Z", "updated_at": "2025-05-13T17:42:01.232Z"}}, {"model": "inventory.product", "pk": 887, "fields": {"name": "FDM1", "code": "1082", "price": "555.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.227Z", "updated_at": "2025-05-13T17:42:01.227Z"}}, {"model": "inventory.product", "pk": 888, "fields": {"name": "FDM3", "code": "1083", "price": "555.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.223Z", "updated_at": "2025-05-13T17:42:01.223Z"}}, {"model": "inventory.product", "pk": 889, "fields": {"name": "FLIX", "code": "1084", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.216Z", "updated_at": "2025-05-13T17:42:01.216Z"}}, {"model": "inventory.product", "pk": 890, "fields": {"name": "FLOWER-1", "code": "1085", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.212Z", "updated_at": "2025-05-13T17:42:01.212Z"}}, {"model": "inventory.product", "pk": 891, "fields": {"name": "FLOWER-3", "code": "1086", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.207Z", "updated_at": "2025-05-13T17:42:01.207Z"}}, {"model": "inventory.product", "pk": 892, "fields": {"name": "FULLA", "code": "1087", "price": "650.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.201Z", "updated_at": "2025-05-13T17:42:01.201Z"}}, {"model": "inventory.product", "pk": 893, "fields": {"name": "GANT", "code": "1088", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.195Z", "updated_at": "2025-05-13T17:42:01.195Z"}}, {"model": "inventory.product", "pk": 894, "fields": {"name": "GARDINIA", "code": "1089", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.191Z", "updated_at": "2025-05-13T17:42:01.191Z"}}, {"model": "inventory.product", "pk": 895, "fields": {"name": "GHOSN", "code": "1090", "price": "585.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.183Z", "updated_at": "2025-05-13T17:42:01.183Z"}}, {"model": "inventory.product", "pk": 896, "fields": {"name": "GRAPE", "code": "1091", "price": "265.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.178Z", "updated_at": "2025-05-13T17:42:01.178Z"}}, {"model": "inventory.product", "pk": 897, "fields": {"name": "HALKAT", "code": "1092", "price": "630.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.173Z", "updated_at": "2025-05-13T17:42:01.173Z"}}, {"model": "inventory.product", "pk": 898, "fields": {"name": "HAVANA-1", "code": "1093", "price": "530.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.167Z", "updated_at": "2025-05-13T17:42:01.167Z"}}, {"model": "inventory.product", "pk": 899, "fields": {"name": "HAVANA-3", "code": "1094", "price": "550.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.162Z", "updated_at": "2025-05-13T17:42:01.162Z"}}, {"model": "inventory.product", "pk": 900, "fields": {"name": "HAVANA-5", "code": "1095", "price": "530.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.157Z", "updated_at": "2025-05-13T17:42:01.157Z"}}, {"model": "inventory.product", "pk": 901, "fields": {"name": "ISTANBUL-1", "code": "1096", "price": "620.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.150Z", "updated_at": "2025-05-13T17:42:01.150Z"}}, {"model": "inventory.product", "pk": 902, "fields": {"name": "ISTANBUL-3", "code": "1097", "price": "620.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.146Z", "updated_at": "2025-05-13T17:42:01.146Z"}}, {"model": "inventory.product", "pk": 903, "fields": {"name": "ISTANBUL-5", "code": "1098", "price": "415.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.141Z", "updated_at": "2025-05-13T17:42:01.141Z"}}, {"model": "inventory.product", "pk": 904, "fields": {"name": "JAGUAR", "code": "1099", "price": "710.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.136Z", "updated_at": "2025-05-13T17:42:01.136Z"}}, {"model": "inventory.product", "pk": 905, "fields": {"name": "JOURY", "code": "1100", "price": "700.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.130Z", "updated_at": "2025-05-13T17:42:01.130Z"}}, {"model": "inventory.product", "pk": 906, "fields": {"name": "K", "code": "1101", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.125Z", "updated_at": "2025-05-13T17:42:01.125Z"}}, {"model": "inventory.product", "pk": 907, "fields": {"name": "K-11", "code": "1102", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.120Z", "updated_at": "2025-05-13T17:42:01.120Z"}}, {"model": "inventory.product", "pk": 908, "fields": {"name": "K-13", "code": "1103", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.114Z", "updated_at": "2025-05-13T17:42:01.114Z"}}, {"model": "inventory.product", "pk": 909, "fields": {"name": "K-15", "code": "1104", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.109Z", "updated_at": "2025-05-13T17:42:01.109Z"}}, {"model": "inventory.product", "pk": 910, "fields": {"name": "K-17", "code": "1105", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.105Z", "updated_at": "2025-05-13T17:42:01.105Z"}}, {"model": "inventory.product", "pk": 911, "fields": {"name": "K-19", "code": "1106", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.098Z", "updated_at": "2025-05-13T17:42:01.098Z"}}, {"model": "inventory.product", "pk": 912, "fields": {"name": "K-2411", "code": "1107", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.093Z", "updated_at": "2025-05-13T17:42:01.093Z"}}, {"model": "inventory.product", "pk": 913, "fields": {"name": "K-3", "code": "1108", "price": "170.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.088Z", "updated_at": "2025-05-13T17:42:01.088Z"}}, {"model": "inventory.product", "pk": 914, "fields": {"name": "K-5", "code": "1109", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.080Z", "updated_at": "2025-05-13T17:42:01.080Z"}}, {"model": "inventory.product", "pk": 915, "fields": {"name": "K-7", "code": "1110", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.076Z", "updated_at": "2025-05-13T17:42:01.076Z"}}, {"model": "inventory.product", "pk": 916, "fields": {"name": "K-9", "code": "1111", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.071Z", "updated_at": "2025-05-13T17:42:01.071Z"}}, {"model": "inventory.product", "pk": 917, "fields": {"name": "KH-492", "code": "1112", "price": "735.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.065Z", "updated_at": "2025-05-13T17:42:01.065Z"}}, {"model": "inventory.product", "pk": 918, "fields": {"name": "LAVIN", "code": "1113", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.061Z", "updated_at": "2025-05-13T17:42:01.061Z"}}, {"model": "inventory.product", "pk": 919, "fields": {"name": "LINES", "code": "1114", "price": "650.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.056Z", "updated_at": "2025-05-13T17:42:01.056Z"}}, {"model": "inventory.product", "pk": 920, "fields": {"name": "LOLO-402", "code": "1115", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.050Z", "updated_at": "2025-05-13T17:42:01.050Z"}}, {"model": "inventory.product", "pk": 921, "fields": {"name": "LUNA-27", "code": "1116", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.046Z", "updated_at": "2025-05-13T17:42:01.046Z"}}, {"model": "inventory.product", "pk": 922, "fields": {"name": "M", "code": "1117", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.041Z", "updated_at": "2025-05-13T17:42:01.041Z"}}, {"model": "inventory.product", "pk": 923, "fields": {"name": "M-3H", "code": "1118", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.035Z", "updated_at": "2025-05-13T17:42:01.035Z"}}, {"model": "inventory.product", "pk": 924, "fields": {"name": "M-4", "code": "1119", "price": "195.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.029Z", "updated_at": "2025-05-13T17:42:01.029Z"}}, {"model": "inventory.product", "pk": 925, "fields": {"name": "MARSHMELLO", "code": "1120", "price": "750.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.024Z", "updated_at": "2025-05-13T17:42:01.024Z"}}, {"model": "inventory.product", "pk": 926, "fields": {"name": "ME-1", "code": "1121", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.018Z", "updated_at": "2025-05-13T17:42:01.018Z"}}, {"model": "inventory.product", "pk": 927, "fields": {"name": "ME-3", "code": "1122", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.012Z", "updated_at": "2025-05-13T17:42:01.012Z"}}, {"model": "inventory.product", "pk": 928, "fields": {"name": "MEGA", "code": "1123", "price": "535.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.007Z", "updated_at": "2025-05-13T17:42:01.007Z"}}, {"model": "inventory.product", "pk": 929, "fields": {"name": "MELODY", "code": "1124", "price": "990.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:01.002Z", "updated_at": "2025-05-13T17:42:01.002Z"}}, {"model": "inventory.product", "pk": 930, "fields": {"name": "MILANO", "code": "1125", "price": "755.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.996Z", "updated_at": "2025-05-13T17:42:00.996Z"}}, {"model": "inventory.product", "pk": 931, "fields": {"name": "M-LINEN", "code": "1126", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.991Z", "updated_at": "2025-05-13T17:42:00.991Z"}}, {"model": "inventory.product", "pk": 932, "fields": {"name": "MM-1033", "code": "1127", "price": "405.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.986Z", "updated_at": "2025-05-13T17:42:00.986Z"}}, {"model": "inventory.product", "pk": 933, "fields": {"name": "MM-1051", "code": "1128", "price": "405.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.980Z", "updated_at": "2025-05-13T17:42:00.980Z"}}, {"model": "inventory.product", "pk": 934, "fields": {"name": "MM-1118", "code": "1129", "price": "250.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.974Z", "updated_at": "2025-05-13T17:42:00.974Z"}}, {"model": "inventory.product", "pk": 935, "fields": {"name": "MM-2012", "code": "1130", "price": "570.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.968Z", "updated_at": "2025-05-13T17:42:00.968Z"}}, {"model": "inventory.product", "pk": 936, "fields": {"name": "MM-2052", "code": "1131", "price": "510.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.962Z", "updated_at": "2025-05-13T17:42:00.962Z"}}, {"model": "inventory.product", "pk": 937, "fields": {"name": "MM-2069", "code": "1132", "price": "490.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.957Z", "updated_at": "2025-05-13T17:42:00.957Z"}}, {"model": "inventory.product", "pk": 938, "fields": {"name": "MM-2090", "code": "1133", "price": "550.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.950Z", "updated_at": "2025-05-13T17:42:00.950Z"}}, {"model": "inventory.product", "pk": 939, "fields": {"name": "MM-2169", "code": "1134", "price": "390.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.945Z", "updated_at": "2025-05-13T17:42:00.945Z"}}, {"model": "inventory.product", "pk": 940, "fields": {"name": "MM-3019", "code": "1135", "price": "545.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.940Z", "updated_at": "2025-05-13T17:42:00.940Z"}}, {"model": "inventory.product", "pk": 941, "fields": {"name": "MM-369", "code": "1136", "price": "885.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.934Z", "updated_at": "2025-05-13T17:42:00.934Z"}}, {"model": "inventory.product", "pk": 942, "fields": {"name": "MM-475", "code": "1137", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.929Z", "updated_at": "2025-05-13T17:42:00.929Z"}}, {"model": "inventory.product", "pk": 943, "fields": {"name": "MM-635", "code": "1138", "price": "710.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.924Z", "updated_at": "2025-05-13T17:42:00.924Z"}}, {"model": "inventory.product", "pk": 944, "fields": {"name": "MM-64", "code": "1139", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.918Z", "updated_at": "2025-05-13T17:42:00.918Z"}}, {"model": "inventory.product", "pk": 945, "fields": {"name": "MM-647", "code": "1140", "price": "555.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.911Z", "updated_at": "2025-05-13T17:42:00.911Z"}}, {"model": "inventory.product", "pk": 946, "fields": {"name": "MM-675", "code": "1141", "price": "590.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.906Z", "updated_at": "2025-05-13T17:42:00.906Z"}}, {"model": "inventory.product", "pk": 947, "fields": {"name": "MM-736", "code": "1142", "price": "700.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.900Z", "updated_at": "2025-05-13T17:42:00.900Z"}}, {"model": "inventory.product", "pk": 948, "fields": {"name": "MM-758", "code": "1143", "price": "765.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.895Z", "updated_at": "2025-05-13T17:42:00.895Z"}}, {"model": "inventory.product", "pk": 949, "fields": {"name": "MM-796", "code": "1144", "price": "700.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.889Z", "updated_at": "2025-05-13T17:42:00.889Z"}}, {"model": "inventory.product", "pk": 950, "fields": {"name": "MM-822", "code": "1145", "price": "930.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.883Z", "updated_at": "2025-05-13T17:42:00.883Z"}}, {"model": "inventory.product", "pk": 951, "fields": {"name": "MM-866", "code": "1146", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.878Z", "updated_at": "2025-05-13T17:42:00.878Z"}}, {"model": "inventory.product", "pk": 952, "fields": {"name": "MM-977", "code": "1147", "price": "575.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.873Z", "updated_at": "2025-05-13T17:42:00.873Z"}}, {"model": "inventory.product", "pk": 953, "fields": {"name": "MM-993", "code": "1148", "price": "420.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.867Z", "updated_at": "2025-05-13T17:42:00.867Z"}}, {"model": "inventory.product", "pk": 954, "fields": {"name": "MORA", "code": "1149", "price": "595.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.862Z", "updated_at": "2025-05-13T17:42:00.862Z"}}, {"model": "inventory.product", "pk": 955, "fields": {"name": "MSHGAR", "code": "1150", "price": "555.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.856Z", "updated_at": "2025-05-13T17:42:00.856Z"}}, {"model": "inventory.product", "pk": 956, "fields": {"name": "NF-35", "code": "1151", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.850Z", "updated_at": "2025-05-13T17:42:00.850Z"}}, {"model": "inventory.product", "pk": 957, "fields": {"name": "NF-40", "code": "1152", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.845Z", "updated_at": "2025-05-13T17:42:00.845Z"}}, {"model": "inventory.product", "pk": 958, "fields": {"name": "NF-46", "code": "1153", "price": "555.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.840Z", "updated_at": "2025-05-13T17:42:00.840Z"}}, {"model": "inventory.product", "pk": 959, "fields": {"name": "NF-52", "code": "1154", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.833Z", "updated_at": "2025-05-13T17:42:00.833Z"}}, {"model": "inventory.product", "pk": 960, "fields": {"name": "NF-65", "code": "1155", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.828Z", "updated_at": "2025-05-13T17:42:00.828Z"}}, {"model": "inventory.product", "pk": 961, "fields": {"name": "NG", "code": "1156", "price": "425.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.823Z", "updated_at": "2025-05-13T17:42:00.823Z"}}, {"model": "inventory.product", "pk": 962, "fields": {"name": "NG-11", "code": "1157", "price": "425.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.816Z", "updated_at": "2025-05-13T17:42:00.816Z"}}, {"model": "inventory.product", "pk": 963, "fields": {"name": "NG-13", "code": "1158", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.811Z", "updated_at": "2025-05-13T17:42:00.811Z"}}, {"model": "inventory.product", "pk": 964, "fields": {"name": "NG-18", "code": "1159", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.806Z", "updated_at": "2025-05-13T17:42:00.806Z"}}, {"model": "inventory.product", "pk": 965, "fields": {"name": "NG-3", "code": "1160", "price": "305.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.800Z", "updated_at": "2025-05-13T17:42:00.800Z"}}, {"model": "inventory.product", "pk": 966, "fields": {"name": "NG-7", "code": "1161", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.795Z", "updated_at": "2025-05-13T17:42:00.795Z"}}, {"model": "inventory.product", "pk": 967, "fields": {"name": "NG-9", "code": "1162", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.790Z", "updated_at": "2025-05-13T17:42:00.790Z"}}, {"model": "inventory.product", "pk": 968, "fields": {"name": "NNG-10", "code": "1163", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.785Z", "updated_at": "2025-05-13T17:42:00.785Z"}}, {"model": "inventory.product", "pk": 969, "fields": {"name": "NNG-12", "code": "1164", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.780Z", "updated_at": "2025-05-13T17:42:00.780Z"}}, {"model": "inventory.product", "pk": 970, "fields": {"name": "NNG-14", "code": "1165", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.775Z", "updated_at": "2025-05-13T17:42:00.775Z"}}, {"model": "inventory.product", "pk": 971, "fields": {"name": "NNG-16", "code": "1166", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.769Z", "updated_at": "2025-05-13T17:42:00.770Z"}}, {"model": "inventory.product", "pk": 972, "fields": {"name": "NNG-18", "code": "1167", "price": "455.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.763Z", "updated_at": "2025-05-13T17:42:00.763Z"}}, {"model": "inventory.product", "pk": 973, "fields": {"name": "NNG-2", "code": "1168", "price": "560.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.759Z", "updated_at": "2025-05-13T17:42:00.759Z"}}, {"model": "inventory.product", "pk": 974, "fields": {"name": "NNG-21", "code": "1169", "price": "395.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.754Z", "updated_at": "2025-05-13T17:42:00.754Z"}}, {"model": "inventory.product", "pk": 975, "fields": {"name": "NNG-24", "code": "1170", "price": "375.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.748Z", "updated_at": "2025-05-13T17:42:00.748Z"}}, {"model": "inventory.product", "pk": 976, "fields": {"name": "NNG-26", "code": "1171", "price": "395.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.743Z", "updated_at": "2025-05-13T17:42:00.743Z"}}, {"model": "inventory.product", "pk": 977, "fields": {"name": "NNG-4", "code": "1172", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.739Z", "updated_at": "2025-05-13T17:42:00.739Z"}}, {"model": "inventory.product", "pk": 978, "fields": {"name": "NNG-6", "code": "1173", "price": "535.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.732Z", "updated_at": "2025-05-13T17:42:00.732Z"}}, {"model": "inventory.product", "pk": 979, "fields": {"name": "NNG-8", "code": "1174", "price": "535.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.727Z", "updated_at": "2025-05-13T17:42:00.727Z"}}, {"model": "inventory.product", "pk": 980, "fields": {"name": "PISTACH", "code": "1175", "price": "780.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.722Z", "updated_at": "2025-05-13T17:42:00.722Z"}}, {"model": "inventory.product", "pk": 981, "fields": {"name": "PUZZLE", "code": "1176", "price": "590.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.716Z", "updated_at": "2025-05-13T17:42:00.716Z"}}, {"model": "inventory.product", "pk": 982, "fields": {"name": "R", "code": "1177", "price": "425.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.711Z", "updated_at": "2025-05-13T17:42:00.711Z"}}, {"model": "inventory.product", "pk": 983, "fields": {"name": "R-11", "code": "1178", "price": "280.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.707Z", "updated_at": "2025-05-13T17:42:00.707Z"}}, {"model": "inventory.product", "pk": 984, "fields": {"name": "R-13", "code": "1179", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.700Z", "updated_at": "2025-05-13T17:42:00.700Z"}}, {"model": "inventory.product", "pk": 985, "fields": {"name": "R-15", "code": "1180", "price": "320.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.696Z", "updated_at": "2025-05-13T17:42:00.696Z"}}, {"model": "inventory.product", "pk": 986, "fields": {"name": "R-17", "code": "1181", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.691Z", "updated_at": "2025-05-13T17:42:00.691Z"}}, {"model": "inventory.product", "pk": 987, "fields": {"name": "R-19", "code": "1182", "price": "515.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.686Z", "updated_at": "2025-05-13T17:42:00.686Z"}}, {"model": "inventory.product", "pk": 988, "fields": {"name": "R-20", "code": "1183", "price": "310.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.680Z", "updated_at": "2025-05-13T17:42:00.680Z"}}, {"model": "inventory.product", "pk": 989, "fields": {"name": "R-22", "code": "1184", "price": "545.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.674Z", "updated_at": "2025-05-13T17:42:00.675Z"}}, {"model": "inventory.product", "pk": 990, "fields": {"name": "RESHA", "code": "1185", "price": "340.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.670Z", "updated_at": "2025-05-13T17:42:00.670Z"}}, {"model": "inventory.product", "pk": 991, "fields": {"name": "RETAJ-413", "code": "1186", "price": "660.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.664Z", "updated_at": "2025-05-13T17:42:00.664Z"}}, {"model": "inventory.product", "pk": 992, "fields": {"name": "RF-1", "code": "1187", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.659Z", "updated_at": "2025-05-13T17:42:00.659Z"}}, {"model": "inventory.product", "pk": 993, "fields": {"name": "RF-4", "code": "1188", "price": "570.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.654Z", "updated_at": "2025-05-13T17:42:00.654Z"}}, {"model": "inventory.product", "pk": 994, "fields": {"name": "RMM-1003", "code": "1189", "price": "325.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.648Z", "updated_at": "2025-05-13T17:42:00.648Z"}}, {"model": "inventory.product", "pk": 995, "fields": {"name": "RMM-1062", "code": "1190", "price": "370.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.643Z", "updated_at": "2025-05-13T17:42:00.643Z"}}, {"model": "inventory.product", "pk": 996, "fields": {"name": "RMM-1072", "code": "1191", "price": "305.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.639Z", "updated_at": "2025-05-13T17:42:00.639Z"}}, {"model": "inventory.product", "pk": 997, "fields": {"name": "RMM-1095", "code": "1192", "price": "355.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.632Z", "updated_at": "2025-05-13T17:42:00.632Z"}}, {"model": "inventory.product", "pk": 998, "fields": {"name": "RMM-759", "code": "1193", "price": "685.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.627Z", "updated_at": "2025-05-13T17:42:00.627Z"}}, {"model": "inventory.product", "pk": 999, "fields": {"name": "RMM-838", "code": "1194", "price": "550.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.622Z", "updated_at": "2025-05-13T17:42:00.622Z"}}, {"model": "inventory.product", "pk": 1000, "fields": {"name": "RMM-905", "code": "1195", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.615Z", "updated_at": "2025-05-13T17:42:00.615Z"}}, {"model": "inventory.product", "pk": 1001, "fields": {"name": "RMM-909", "code": "1196", "price": "505.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.610Z", "updated_at": "2025-05-13T17:42:00.610Z"}}, {"model": "inventory.product", "pk": 1002, "fields": {"name": "RMM-915", "code": "1197", "price": "660.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.605Z", "updated_at": "2025-05-13T17:42:00.605Z"}}, {"model": "inventory.product", "pk": 1003, "fields": {"name": "RMM-926", "code": "1198", "price": "640.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.599Z", "updated_at": "2025-05-13T17:42:00.599Z"}}, {"model": "inventory.product", "pk": 1004, "fields": {"name": "RMM-950", "code": "1199", "price": "440.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.594Z", "updated_at": "2025-05-13T17:42:00.594Z"}}, {"model": "inventory.product", "pk": 1005, "fields": {"name": "ROONEY", "code": "1200", "price": "990.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.589Z", "updated_at": "2025-05-13T17:42:00.589Z"}}, {"model": "inventory.product", "pk": 1006, "fields": {"name": "SALASEL", "code": "1201", "price": "640.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.583Z", "updated_at": "2025-05-13T17:42:00.583Z"}}, {"model": "inventory.product", "pk": 1007, "fields": {"name": "SAVIO", "code": "1202", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.579Z", "updated_at": "2025-05-13T17:42:00.579Z"}}, {"model": "inventory.product", "pk": 1008, "fields": {"name": "SPIDER", "code": "1203", "price": "345.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.574Z", "updated_at": "2025-05-13T17:42:00.574Z"}}, {"model": "inventory.product", "pk": 1009, "fields": {"name": "SQURE", "code": "1204", "price": "570.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.567Z", "updated_at": "2025-05-13T17:42:00.567Z"}}, {"model": "inventory.product", "pk": 1010, "fields": {"name": "STORY-1", "code": "1205", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.562Z", "updated_at": "2025-05-13T17:42:00.562Z"}}, {"model": "inventory.product", "pk": 1011, "fields": {"name": "STORY-3", "code": "1206", "price": "460.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.558Z", "updated_at": "2025-05-13T17:42:00.558Z"}}, {"model": "inventory.product", "pk": 1012, "fields": {"name": "TINA", "code": "1207", "price": "380.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.552Z", "updated_at": "2025-05-13T17:42:00.552Z"}}, {"model": "inventory.product", "pk": 1013, "fields": {"name": "TR", "code": "1208", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.546Z", "updated_at": "2025-05-13T17:42:00.546Z"}}, {"model": "inventory.product", "pk": 1014, "fields": {"name": "TR-2", "code": "1209", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.541Z", "updated_at": "2025-05-13T17:42:00.541Z"}}, {"model": "inventory.product", "pk": 1015, "fields": {"name": "TR-4", "code": "1210", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.536Z", "updated_at": "2025-05-13T17:42:00.536Z"}}, {"model": "inventory.product", "pk": 1016, "fields": {"name": "TR-6", "code": "1211", "price": "230.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.530Z", "updated_at": "2025-05-13T17:42:00.530Z"}}, {"model": "inventory.product", "pk": 1017, "fields": {"name": "TR-8", "code": "1212", "price": "230.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.525Z", "updated_at": "2025-05-13T17:42:00.525Z"}}, {"model": "inventory.product", "pk": 1018, "fields": {"name": "TURK-1", "code": "1213", "price": "885.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.520Z", "updated_at": "2025-05-13T17:42:00.520Z"}}, {"model": "inventory.product", "pk": 1019, "fields": {"name": "TURK-11", "code": "1214", "price": "650.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.514Z", "updated_at": "2025-05-13T17:42:00.514Z"}}, {"model": "inventory.product", "pk": 1020, "fields": {"name": "TURK-13", "code": "1215", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.508Z", "updated_at": "2025-05-13T17:42:00.508Z"}}, {"model": "inventory.product", "pk": 1021, "fields": {"name": "TURK-3", "code": "1216", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.502Z", "updated_at": "2025-05-13T17:42:00.502Z"}}, {"model": "inventory.product", "pk": 1022, "fields": {"name": "TURK-5", "code": "1217", "price": "750.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.496Z", "updated_at": "2025-05-13T17:42:00.496Z"}}, {"model": "inventory.product", "pk": 1023, "fields": {"name": "TURK-7", "code": "1218", "price": "545.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.491Z", "updated_at": "2025-05-13T17:42:00.491Z"}}, {"model": "inventory.product", "pk": 1024, "fields": {"name": "TURK-9", "code": "1219", "price": "495.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.485Z", "updated_at": "2025-05-13T17:42:00.485Z"}}, {"model": "inventory.product", "pk": 1025, "fields": {"name": "VALAS", "code": "1220", "price": "560.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.479Z", "updated_at": "2025-05-13T17:42:00.479Z"}}, {"model": "inventory.product", "pk": 1026, "fields": {"name": "WAVES", "code": "1221", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.474Z", "updated_at": "2025-05-13T17:42:00.474Z"}}, {"model": "inventory.product", "pk": 1027, "fields": {"name": "WNG", "code": "1222", "price": "200.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.469Z", "updated_at": "2025-05-13T17:42:00.469Z"}}, {"model": "inventory.product", "pk": 1028, "fields": {"name": "WNG-11", "code": "1223", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.463Z", "updated_at": "2025-05-13T17:42:00.463Z"}}, {"model": "inventory.product", "pk": 1029, "fields": {"name": "WNG-13", "code": "1224", "price": "470.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.458Z", "updated_at": "2025-05-13T17:42:00.458Z"}}, {"model": "inventory.product", "pk": 1030, "fields": {"name": "WNG-15", "code": "1225", "price": "535.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.453Z", "updated_at": "2025-05-13T17:42:00.453Z"}}, {"model": "inventory.product", "pk": 1031, "fields": {"name": "WNG-17", "code": "1226", "price": "240.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.447Z", "updated_at": "2025-05-13T17:42:00.447Z"}}, {"model": "inventory.product", "pk": 1032, "fields": {"name": "WNG-2", "code": "1227", "price": "320.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.442Z", "updated_at": "2025-05-13T17:42:00.442Z"}}, {"model": "inventory.product", "pk": 1033, "fields": {"name": "WNG-21", "code": "1228", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.437Z", "updated_at": "2025-05-13T17:42:00.437Z"}}, {"model": "inventory.product", "pk": 1034, "fields": {"name": "WNG-7", "code": "1229", "price": "480.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.429Z", "updated_at": "2025-05-13T17:42:00.429Z"}}, {"model": "inventory.product", "pk": 1035, "fields": {"name": "WNG-9", "code": "1230", "price": "285.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.424Z", "updated_at": "2025-05-13T17:42:00.424Z"}}, {"model": "inventory.product", "pk": 1036, "fields": {"name": "WNG-8", "code": "1231", "price": "470.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.416Z", "updated_at": "2025-05-13T17:42:00.416Z"}}, {"model": "inventory.product", "pk": 1037, "fields": {"name": "ZIGZAG", "code": "1232", "price": "260.00", "currency": "EGP", "category": null, "description": "2.8", "minimum_stock": 100, "created_at": "2025-05-13T17:42:00.409Z", "updated_at": "2025-05-13T17:42:00.409Z"}}, {"model": "installations.technician", "pk": 1, "fields": {"name": "زكي طحاوي", "phone": "01204122216", "specialization": "", "is_active": true, "created_at": "2025-07-17T17:09:11.945Z", "updated_at": "2025-07-17T17:09:11.945Z"}}, {"model": "installations.driver", "pk": 1, "fields": {"name": "crm", "phone": "1221", "license_number": "", "vehicle_number": "", "is_active": true, "created_at": "2025-07-17T17:09:21.465Z", "updated_at": "2025-07-17T17:09:21.465Z"}}, {"model": "installations.installationteam", "pk": 1, "fields": {"name": "نظام الخواجه", "driver": 1, "is_active": true, "created_at": "2025-07-17T17:09:43.985Z", "updated_at": "2025-07-17T17:09:43.985Z", "technicians": [1]}}, {"model": "installations.modificationerrortype", "pk": 1, "fields": {"name": "الوصف", "description": "", "is_active": true, "created_at": "2025-07-15T17:32:33.979Z", "updated_at": "2025-07-15T17:32:33.979Z"}}, {"model": "accounts.user", "pk": 2, "fields": {"password": "pbkdf2_sha256$600000$Cs1lflBHjvnnCXDDNG5A0l$v67rYJZjrogIf9g3fqACr00zHIRr67v4c53TUwwr5Bk=", "last_login": "2025-07-22T09:34:23.155Z", "is_superuser": true, "username": "Dr.<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-05-26T17:40:38Z", "image": "", "phone": "***********", "branch": 1, "is_inspection_technician": false, "default_theme": "custom-theme", "updated_at": "2025-06-14T07:49:27.983Z", "groups": [], "user_permissions": [], "departments": [34, 35, 36, 37, 38, 39, 40, 8]}}, {"model": "accounts.user", "pk": 3, "fields": {"password": "pbkdf2_sha256$600000$YfdYUsvGMntNN8C0P6b9R2$kW+IjksXCZ6gkLBrFwhHnYVvQ6JL7Xd3JC6mxwuXwsA=", "last_login": "2025-07-23T12:38:22.417Z", "is_superuser": true, "username": "a<PERSON>aa", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-05-27T09:46:12Z", "image": "", "phone": "", "branch": 1, "is_inspection_technician": false, "default_theme": "custom-theme", "updated_at": "2025-06-14T07:49:19.564Z", "groups": [], "user_permissions": [89, 90, 91, 92, 85, 86, 87, 88, 95, 96, 97, 98, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 167, 168, 169, 170, 175, 176, 177, 178, 179, 180, 181, 182], "departments": [34, 35, 37]}}, {"model": "accounts.user", "pk": 4, "fields": {"password": "pbkdf2_sha256$600000$GjeVzeZx4M4OCrqweKh7tq$agqCc3rk30KLZJCLAb7RwhXJIsO+o/0418QYVuGBbHE=", "last_login": "2025-07-24T10:56:08.851Z", "is_superuser": true, "username": "admin", "first_name": "مدير", "last_name": "النظام", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-06-19T17:26:50Z", "image": "", "phone": "", "branch": 1, "is_inspection_technician": false, "default_theme": "default", "updated_at": "2025-07-16T16:53:56.808Z", "groups": [], "user_permissions": [], "departments": []}}, {"model": "accounts.user", "pk": 5, "fields": {"password": "pbkdf2_sha256$600000$vaQF1O57Q7SiYUdTSo9KHa$n4lH5wn2hZR4udOuY/W2nwCnEP+SyVzQiEVACt3yxYg=", "last_login": "2025-07-24T11:35:10.227Z", "is_superuser": true, "username": "zakee.tahawi", "first_name": "مدير", "last_name": "النظام", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-05-22T14:57:06Z", "image": "", "phone": "", "branch": 1, "is_inspection_technician": true, "default_theme": "custom-theme", "updated_at": "2025-06-14T07:49:06.990Z", "groups": [], "user_permissions": [], "departments": []}}, {"model": "accounts.user", "pk": 6, "fields": {"password": "pbkdf2_sha256$600000$v4QcfdWrwHKKWln4TXNVfW$rQ99Oufxt0VZHCftRfjtNVzUL0JFznQPnxPl03TAxas=", "last_login": "2025-06-21T16:40:12.707Z", "is_superuser": false, "username": "philo", "first_name": "philo", "last_name": "wael", "email": "", "is_staff": false, "is_active": true, "date_joined": "2025-06-21T16:35:52Z", "image": "", "phone": "***********", "branch": null, "is_inspection_technician": false, "default_theme": "default", "updated_at": "2025-06-21T16:36:41.795Z", "groups": [], "user_permissions": [], "departments": []}}, {"model": "accounts.user", "pk": 7, "fields": {"password": "pbkdf2_sha256$600000$mKBB9FYlXPWS7uzM2AtyqR$93FzpMcXpZhqammrh94R6CAJiv8hasdoWPnuKTTCjxw=", "last_login": "2025-06-04T17:44:05Z", "is_superuser": true, "username": "wael", "first_name": "وائل", "last_name": "الخواجة", "email": "", "is_staff": true, "is_active": true, "date_joined": "2025-05-26T16:35:26Z", "image": "", "phone": "", "branch": 1, "is_inspection_technician": false, "default_theme": "custom-theme", "updated_at": "2025-06-14T07:49:51.981Z", "groups": [], "user_permissions": [], "departments": [34, 35, 36, 37, 38, 39, 40]}}, {"model": "accounts.user", "pk": 8, "fields": {"password": "pbkdf2_sha256$600000$Bkw5TMi02vk1EBZTDSyogN$QdEaUa6JhlS4THFZNGf6aCBmnTWiMpo32L+jfc/DdYE=", "last_login": "2025-07-20T14:50:30.895Z", "is_superuser": true, "username": "Iman", "first_name": "Iman", "last_name": "", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-05-27T09:54:01Z", "image": "", "phone": "", "branch": 1, "is_inspection_technician": false, "default_theme": "custom-theme", "updated_at": "2025-06-14T07:50:33.940Z", "groups": [], "user_permissions": [89, 90, 91, 92, 85, 86, 87, 88, 95, 96, 97, 98, 167, 168, 169, 170, 175, 176, 177, 178, 179, 180, 181, 182], "departments": [34, 35]}}, {"model": "accounts.branch", "pk": 1, "fields": {"code": "1", "name": "الادارة", "address": "", "phone": null, "email": null, "is_main_branch": true, "is_active": true}}, {"model": "accounts.branch", "pk": 2, "fields": {"code": "14", "name": "Open Air", "address": "مدينتي - اوبن اير مول", "phone": "***********", "email": "<EMAIL>", "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 4, "fields": {"code": "2", "name": "فرع الازهر 1 \"طلعت\"", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 5, "fields": {"code": "3", "name": "فرع الازهر 3 \"الحمزاوي\"", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 6, "fields": {"code": "4", "name": "فرع الازهر 2 \"Karam\"", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 7, "fields": {"code": "5", "name": "فرع القلعه", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 8, "fields": {"code": "7", "name": "فرع النحاس", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 9, "fields": {"code": "8", "name": "فرع المول", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 10, "fields": {"code": "10", "name": "فرع المعادي", "address": "", "phone": "***********", "email": "<EMAIL>", "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 11, "fields": {"code": "11", "name": "فرع الدقي", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 12, "fields": {"code": "9", "name": "فرع اكتوبر", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 13, "fields": {"code": "12", "name": "فرع فيصل", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 14, "fields": {"code": "13", "name": "فرع الشيخ زايد", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 15, "fields": {"code": "15", "name": "east hup", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 16, "fields": {"code": "16", "name": "فرع النزهه", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 17, "fields": {"code": "18", "name": "online & hotline", "address": "", "phone": "19148", "email": "<EMAIL>", "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 18, "fields": {"code": "17", "name": "فرع الساحل الشمالي", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.branch", "pk": 19, "fields": {"code": "19", "name": "قسم مبيعات الجمله (B2B)", "address": "", "phone": "***********", "email": null, "is_main_branch": false, "is_active": true}}, {"model": "accounts.department", "pk": 8, "fields": {"name": "إدارة البيانات", "code": "data_management", "department_type": "department", "description": null, "icon": "fas fa-database", "url_name": "data_management:index", "is_active": true, "is_core": true, "order": 80, "parent": null, "has_pages": true, "manager": null}}, {"model": "accounts.department", "pk": 34, "fields": {"name": "العملاء", "code": "customers", "department_type": "department", "description": null, "icon": "fas fa-users", "url_name": "customers:customer_list", "is_active": true, "is_core": true, "order": 10, "parent": null, "has_pages": false, "manager": null}}, {"model": "accounts.department", "pk": 35, "fields": {"name": "الطلبات", "code": "orders", "department_type": "department", "description": null, "icon": "fas fa-shopping-cart", "url_name": "orders:order_list", "is_active": true, "is_core": true, "order": 20, "parent": null, "has_pages": false, "manager": null}}, {"model": "accounts.department", "pk": 36, "fields": {"name": "المخزون", "code": "inventory", "department_type": "department", "description": null, "icon": "fas fa-boxes", "url_name": "inventory:dashboard", "is_active": true, "is_core": true, "order": 30, "parent": null, "has_pages": false, "manager": null}}, {"model": "accounts.department", "pk": 37, "fields": {"name": "المعاينات", "code": "inspections", "department_type": "department", "description": null, "icon": "fas fa-clipboard-check", "url_name": "inspections:inspection_list", "is_active": true, "is_core": true, "order": 40, "parent": null, "has_pages": false, "manager": null}}, {"model": "accounts.department", "pk": 38, "fields": {"name": "التركيبات", "code": "installations", "department_type": "department", "description": null, "icon": "fas fa-tools", "url_name": "installations:dashboard", "is_active": true, "is_core": true, "order": 50, "parent": null, "has_pages": false, "manager": null}}, {"model": "accounts.department", "pk": 39, "fields": {"name": "المصنع", "code": "factory", "department_type": "department", "description": null, "icon": "fas fa-industry", "url_name": "factory:factory_list", "is_active": true, "is_core": true, "order": 60, "parent": null, "has_pages": false, "manager": null}}, {"model": "accounts.department", "pk": 40, "fields": {"name": "التقارير", "code": "reports", "department_type": "department", "description": null, "icon": "fas fa-chart-bar", "url_name": "reports:dashboard", "is_active": true, "is_core": true, "order": 70, "parent": null, "has_pages": false, "manager": null}}, {"model": "accounts.notification", "pk": 12, "fields": {"title": "طلب معاينة جديد", "message": "تم إنشاء طلب معاينة جديد للعميل zakee tahawi من الطلب 16-0001-0001", "priority": "high", "created_at": "2025-06-04T15:16:56.109Z", "updated_at": "2025-06-04T16:13:39.398Z", "read_at": "2025-06-04T16:13:39.398Z", "is_read": true, "read_by": null, "sender": 5, "target_department": 37, "target_branch": 16, "content_type": null, "object_id": null, "sender_department": null, "target_users": []}}, {"model": "accounts.notification", "pk": 14, "fields": {"title": "طلب معاينة جديد", "message": "تم إنشاء طلب معاينة جديد للعميل zakee tahawi من الطلب 16-0002-0001", "priority": "high", "created_at": "2025-06-04T16:57:19.337Z", "updated_at": "2025-06-04T16:58:32.521Z", "read_at": "2025-06-04T16:58:32.521Z", "is_read": true, "read_by": 5, "sender": 5, "target_department": 37, "target_branch": 16, "content_type": null, "object_id": null, "sender_department": null, "target_users": []}}, {"model": "accounts.notification", "pk": 15, "fields": {"title": "طلب معاينة جديد", "message": "تم إنشاء طلب معاينة جديد للعميل محمد السيد عوض من الطلب 01-0025-0001", "priority": "high", "created_at": "2025-06-11T09:21:56.108Z", "updated_at": "2025-06-11T11:29:04.217Z", "read_at": "2025-06-11T11:29:04.217Z", "is_read": true, "read_by": 5, "sender": 5, "target_department": 37, "target_branch": 18, "content_type": null, "object_id": null, "sender_department": null, "target_users": []}}, {"model": "accounts.notification", "pk": 16, "fields": {"title": "طلب معاينة جديد", "message": "تم إنشاء طلب معاينة جديد للعميل باسم محمد يوسف من الطلب 14-0003-0001", "priority": "high", "created_at": "2025-06-16T17:38:46.740Z", "updated_at": "2025-06-16T18:14:13.567Z", "read_at": "2025-06-16T18:14:13.567Z", "is_read": true, "read_by": 5, "sender": 5, "target_department": 37, "target_branch": 18, "content_type": null, "object_id": null, "sender_department": null, "target_users": []}}, {"model": "accounts.notification", "pk": 17, "fields": {"title": "طلب معاينة جديد", "message": "تم إنشاء طلب معاينة جديد للعميل مصطفى الرفاعي من الطلب 12-0003-0001", "priority": "high", "created_at": "2025-06-17T13:21:22.194Z", "updated_at": "2025-06-17T16:36:23.288Z", "read_at": "2025-06-17T16:36:23.288Z", "is_read": true, "read_by": 5, "sender": 5, "target_department": 37, "target_branch": 18, "content_type": null, "object_id": null, "sender_department": null, "target_users": []}}, {"model": "accounts.notification", "pk": 18, "fields": {"title": "تحديث حالة الطلب #5-0104-0001", "message": "تم تحديث حالة الطلب", "priority": "medium", "created_at": "2025-07-17T14:02:43.023Z", "updated_at": "2025-07-19T16:13:03.735Z", "read_at": "2025-07-19T16:13:03.735Z", "is_read": true, "read_by": 4, "sender": 5, "target_department": 34, "target_branch": 1, "content_type": 42, "object_id": 2684, "sender_department": 35, "target_users": []}}, {"model": "accounts.notification", "pk": 19, "fields": {"title": "تحديث حالة الطلب #5-0104-0001", "message": "تم تحديث حالة الطلب", "priority": "medium", "created_at": "2025-07-17T14:02:43.029Z", "updated_at": "2025-07-19T16:13:03.733Z", "read_at": "2025-07-19T16:13:03.733Z", "is_read": true, "read_by": 4, "sender": 5, "target_department": 34, "target_branch": 1, "content_type": 42, "object_id": 2684, "sender_department": 35, "target_users": []}}, {"model": "accounts.notification", "pk": 20, "fields": {"title": "تحديث حالة الطلب #1-0001-0001", "message": "تم تحديث حالة الطلب", "priority": "medium", "created_at": "2025-07-17T14:03:46.054Z", "updated_at": "2025-07-19T16:13:03.731Z", "read_at": "2025-07-19T16:13:03.731Z", "is_read": true, "read_by": 4, "sender": 5, "target_department": 34, "target_branch": 1, "content_type": 42, "object_id": 2685, "sender_department": 35, "target_users": []}}, {"model": "accounts.notification", "pk": 21, "fields": {"title": "تحديث حالة الطلب #1-0001-0001", "message": "تم تحديث حالة الطلب", "priority": "medium", "created_at": "2025-07-17T14:03:46.061Z", "updated_at": "2025-07-19T16:13:03.729Z", "read_at": "2025-07-19T16:13:03.729Z", "is_read": true, "read_by": 4, "sender": 5, "target_department": 34, "target_branch": 1, "content_type": 42, "object_id": 2685, "sender_department": 35, "target_users": []}}, {"model": "accounts.notification", "pk": 22, "fields": {"title": "تحديث حالة الطلب #1-0003-0001", "message": "تم تحديث حالة الطلب", "priority": "medium", "created_at": "2025-07-17T17:32:18.973Z", "updated_at": "2025-07-19T16:13:03.727Z", "read_at": "2025-07-19T16:13:03.727Z", "is_read": true, "read_by": 4, "sender": 4, "target_department": 34, "target_branch": 1, "content_type": 42, "object_id": 2688, "sender_department": 35, "target_users": []}}, {"model": "accounts.notification", "pk": 23, "fields": {"title": "تحديث حالة الطلب #1-0003-0001", "message": "تم تحديث حالة الطلب", "priority": "medium", "created_at": "2025-07-17T17:32:18.980Z", "updated_at": "2025-07-19T16:13:03.719Z", "read_at": "2025-07-19T16:13:03.719Z", "is_read": true, "read_by": 4, "sender": 4, "target_department": 34, "target_branch": 1, "content_type": 42, "object_id": 2688, "sender_department": 35, "target_users": []}}, {"model": "accounts.companyinfo", "pk": 1, "fields": {"version": "1.0.0", "release_date": "2025-04-30", "developer": "zakee tahawi", "working_hours": "10:00am to 10:00pm", "name": "Elkhawaga", "copyright_text": "جميع الحقوق محفوظة لشركة الخواجة للستائر والمفروشات تطوير zakee tahawi", "logo": "", "header_logo": "", "address": "", "phone": "19148", "email": "<EMAIL>", "tax_number": null, "commercial_register": null, "website": "http://www.elkhawaga.com", "social_links": null, "description": "", "facebook": null, "twitter": null, "instagram": null, "linkedin": null, "about": "", "vision": "", "mission": "", "primary_color": null, "secondary_color": null, "accent_color": null}}, {"model": "accounts.salesperson", "pk": 1, "fields": {"name": "zakee tahawi", "employee_number": "268", "branch": 1, "phone": "***********", "email": null, "address": "cairo", "is_active": true, "notes": "", "created_at": "2025-05-03T15:52:43.869Z", "updated_at": "2025-05-03T15:52:43.869Z"}}, {"model": "accounts.salesperson", "pk": 2, "fields": {"name": "رضا محمود", "employee_number": "172", "branch": 2, "phone": "***********", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-28T10:25:41.751Z", "updated_at": "2025-05-29T11:57:42.012Z"}}, {"model": "accounts.salesperson", "pk": 3, "fields": {"name": "احمد سعيد زين", "employee_number": "275", "branch": 2, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T11:58:31.995Z", "updated_at": "2025-05-29T11:58:31.995Z"}}, {"model": "accounts.salesperson", "pk": 4, "fields": {"name": "سيد ادم", "employee_number": "298", "branch": 2, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T11:59:06.744Z", "updated_at": "2025-05-29T11:59:06.744Z"}}, {"model": "accounts.salesperson", "pk": 5, "fields": {"name": "مجدي الهم خليل معوض", "employee_number": "12", "branch": 12, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:11:31.108Z", "updated_at": "2025-05-29T12:11:31.108Z"}}, {"model": "accounts.salesperson", "pk": 6, "fields": {"name": "شنوده رفعت عبدالشهيد", "employee_number": "14", "branch": 12, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:12:37.884Z", "updated_at": "2025-05-29T12:12:37.884Z"}}, {"model": "accounts.salesperson", "pk": 7, "fields": {"name": "نادر ايمن عبدالغني منصور", "employee_number": "111", "branch": 12, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:13:38.216Z", "updated_at": "2025-05-29T12:13:38.216Z"}}, {"model": "accounts.salesperson", "pk": 8, "fields": {"name": "سيد حامد عبدا<PERSON>ر<PERSON>ول احمد", "employee_number": "309", "branch": 12, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:14:06.076Z", "updated_at": "2025-05-29T12:14:06.076Z"}}, {"model": "accounts.salesperson", "pk": 9, "fields": {"name": "اميل يوسف سامي غطاس", "employee_number": "62", "branch": 11, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:16:03.241Z", "updated_at": "2025-05-29T12:16:03.241Z"}}, {"model": "accounts.salesperson", "pk": 10, "fields": {"name": "يوسف لمعي بدروس", "employee_number": "64", "branch": 11, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:24:54.629Z", "updated_at": "2025-05-29T12:24:54.629Z"}}, {"model": "accounts.salesperson", "pk": 11, "fields": {"name": "هانى صالح زكى سليمان", "employee_number": "89", "branch": 7, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:26:32.899Z", "updated_at": "2025-05-29T12:26:32.899Z"}}, {"model": "accounts.salesperson", "pk": 12, "fields": {"name": "خليل ابراهيم محمود محمد", "employee_number": "90", "branch": 7, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:27:03.052Z", "updated_at": "2025-05-29T12:27:03.052Z"}}, {"model": "accounts.salesperson", "pk": 13, "fields": {"name": "عواد كمال عواد خليفة", "employee_number": "252", "branch": 7, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:27:43.232Z", "updated_at": "2025-05-29T12:27:43.232Z"}}, {"model": "accounts.salesperson", "pk": 14, "fields": {"name": "حسام صبحى بباوى بولس", "employee_number": "98", "branch": 10, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:31:18.440Z", "updated_at": "2025-05-29T12:31:18.440Z"}}, {"model": "accounts.salesperson", "pk": 15, "fields": {"name": "محمد احمد صديق خميس", "employee_number": "99", "branch": 10, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:31:39.834Z", "updated_at": "2025-05-29T12:31:39.834Z"}}, {"model": "accounts.salesperson", "pk": 16, "fields": {"name": "مصط<PERSON><PERSON> على محمد محمود", "employee_number": "103", "branch": 10, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:32:04.427Z", "updated_at": "2025-05-29T12:32:04.427Z"}}, {"model": "accounts.salesperson", "pk": 17, "fields": {"name": "محمد طارق توفيق ابوالنجا", "employee_number": "171", "branch": 10, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:33:56.719Z", "updated_at": "2025-05-29T12:33:56.719Z"}}, {"model": "accounts.salesperson", "pk": 18, "fields": {"name": "محمود بهجت رشاد عثمان", "employee_number": "102", "branch": 9, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:35:20.161Z", "updated_at": "2025-05-29T12:35:20.161Z"}}, {"model": "accounts.salesperson", "pk": 19, "fields": {"name": "بدور محمود سيد عبده سيد", "employee_number": "106", "branch": 9, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:36:06.403Z", "updated_at": "2025-05-29T12:36:06.403Z"}}, {"model": "accounts.salesperson", "pk": 20, "fields": {"name": "مينا عزت عبدالملاك غطاس", "employee_number": "107", "branch": 9, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:36:43.954Z", "updated_at": "2025-05-29T12:36:43.954Z"}}, {"model": "accounts.salesperson", "pk": 21, "fields": {"name": "محمد السيد احمد السيد", "employee_number": "110", "branch": 9, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:37:21.432Z", "updated_at": "2025-05-29T12:37:21.432Z"}}, {"model": "accounts.salesperson", "pk": 22, "fields": {"name": "محمد عربى عرفات محمود", "employee_number": "119", "branch": 8, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:38:25.238Z", "updated_at": "2025-05-29T12:38:25.238Z"}}, {"model": "accounts.salesperson", "pk": 23, "fields": {"name": "عصام محمود احمد على", "employee_number": "120", "branch": 8, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:38:59.444Z", "updated_at": "2025-05-29T12:38:59.444Z"}}, {"model": "accounts.salesperson", "pk": 24, "fields": {"name": "صبحي عزير صبحي", "employee_number": "400", "branch": 8, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:39:49.484Z", "updated_at": "2025-05-29T12:39:49.484Z"}}, {"model": "accounts.salesperson", "pk": 25, "fields": {"name": "ميل<PERSON> بباوى يوسف بباوى", "employee_number": "63", "branch": 16, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:40:59.560Z", "updated_at": "2025-05-29T12:40:59.560Z"}}, {"model": "accounts.salesperson", "pk": 26, "fields": {"name": "ماجد سمير بخيت جندى", "employee_number": "123", "branch": 16, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:41:30.869Z", "updated_at": "2025-05-29T12:41:30.869Z"}}, {"model": "accounts.salesperson", "pk": 27, "fields": {"name": "اشرف حسن محمد حسن", "employee_number": "380", "branch": 16, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:42:13.343Z", "updated_at": "2025-05-29T12:42:13.343Z"}}, {"model": "accounts.salesperson", "pk": 28, "fields": {"name": "مينا صلاح ذكى سليمان", "employee_number": "389", "branch": 16, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:42:46.639Z", "updated_at": "2025-05-29T12:42:46.639Z"}}, {"model": "accounts.salesperson", "pk": 29, "fields": {"name": "احمد برهان فاروق", "employee_number": "413", "branch": 16, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:43:41.813Z", "updated_at": "2025-05-29T12:43:41.813Z"}}, {"model": "accounts.salesperson", "pk": 30, "fields": {"name": "جورج وليم وهبه حنا", "employee_number": "118", "branch": 15, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:45:07.177Z", "updated_at": "2025-05-29T12:45:07.177Z"}}, {"model": "accounts.salesperson", "pk": 31, "fields": {"name": "طارق صفوت محم<PERSON> خطاب", "employee_number": "292", "branch": 15, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:45:34.495Z", "updated_at": "2025-05-29T12:45:34.495Z"}}, {"model": "accounts.salesperson", "pk": 32, "fields": {"name": "ايمن الهم خليل معوض", "employee_number": "125", "branch": 14, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:46:29.372Z", "updated_at": "2025-05-29T12:46:29.372Z"}}, {"model": "accounts.salesperson", "pk": 33, "fields": {"name": "احمد حسنى احمد محمد قناوى", "employee_number": "136", "branch": 14, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:47:15.530Z", "updated_at": "2025-05-29T12:47:15.530Z"}}, {"model": "accounts.salesperson", "pk": 35, "fields": {"name": "حسام مسعود السيد احمد", "employee_number": "134", "branch": 4, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:48:37.972Z", "updated_at": "2025-05-29T12:48:37.972Z"}}, {"model": "accounts.salesperson", "pk": 36, "fields": {"name": "احمد محمد عبدالحفيظ محمود", "employee_number": "253", "branch": 4, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:49:16.952Z", "updated_at": "2025-05-29T12:49:16.952Z"}}, {"model": "accounts.salesperson", "pk": 37, "fields": {"name": "احمد  عصام جودة قاسم", "employee_number": "112", "branch": 13, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:51:38.574Z", "updated_at": "2025-05-29T12:51:38.574Z"}}, {"model": "accounts.salesperson", "pk": 38, "fields": {"name": "طه احمد طه حسنين", "employee_number": "138", "branch": 13, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:57:55.798Z", "updated_at": "2025-05-29T12:57:55.798Z"}}, {"model": "accounts.salesperson", "pk": 39, "fields": {"name": "هانى بخيت حبيب بخيت", "employee_number": "139", "branch": 13, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:58:19.128Z", "updated_at": "2025-05-29T12:58:19.128Z"}}, {"model": "accounts.salesperson", "pk": 40, "fields": {"name": "على محمود على احمد", "employee_number": "245", "branch": 13, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T12:58:39.054Z", "updated_at": "2025-05-29T12:58:39.054Z"}}, {"model": "accounts.salesperson", "pk": 41, "fields": {"name": "فرحان فوزى محمد ابراهيم", "employee_number": "17", "branch": 6, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:04:53.296Z", "updated_at": "2025-05-29T13:04:53.296Z"}}, {"model": "accounts.salesperson", "pk": 42, "fields": {"name": "وائل رشدى عبد السيد", "employee_number": "145", "branch": 6, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:05:34.861Z", "updated_at": "2025-05-29T13:05:34.861Z"}}, {"model": "accounts.salesperson", "pk": 43, "fields": {"name": "ارميا فريد راسله يوسف", "employee_number": "147", "branch": 6, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:06:21.927Z", "updated_at": "2025-05-29T13:06:21.927Z"}}, {"model": "accounts.salesperson", "pk": 44, "fields": {"name": "احمد صلاح احمد محمد على", "employee_number": "148", "branch": 6, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:07:21.381Z", "updated_at": "2025-05-29T13:07:21.381Z"}}, {"model": "accounts.salesperson", "pk": 45, "fields": {"name": "ابراهيم سليمان اسكندر ابراهيم", "employee_number": "418", "branch": 6, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:08:39.342Z", "updated_at": "2025-05-29T13:08:39.342Z"}}, {"model": "accounts.salesperson", "pk": 46, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> السيد احمد على", "employee_number": "133", "branch": 5, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:10:00.711Z", "updated_at": "2025-05-29T13:10:00.711Z"}}, {"model": "accounts.salesperson", "pk": 47, "fields": {"name": "اسحق عبدالنور عبده مرجان", "employee_number": "157", "branch": 5, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:10:40.011Z", "updated_at": "2025-05-29T13:10:40.011Z"}}, {"model": "accounts.salesperson", "pk": 48, "fields": {"name": "سيد محمد حسن محمد", "employee_number": "159", "branch": 5, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-29T13:11:11.779Z", "updated_at": "2025-05-29T13:11:11.779Z"}}, {"model": "accounts.salesperson", "pk": 51, "fields": {"name": "احمد فت<PERSON>ى عدلى محمد", "employee_number": "289", "branch": 19, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-30T08:54:01.541Z", "updated_at": "2025-05-30T08:54:01.541Z"}}, {"model": "accounts.salesperson", "pk": 52, "fields": {"name": "نادر عصام عبد<PERSON><PERSON><PERSON><PERSON>ي<PERSON> احمد", "employee_number": "68", "branch": 19, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-30T08:55:37.415Z", "updated_at": "2025-05-30T08:55:37.415Z"}}, {"model": "accounts.salesperson", "pk": 53, "fields": {"name": "بيشوى يوسف شحاته فهيم بطرس", "employee_number": "69", "branch": 19, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-30T08:55:53.887Z", "updated_at": "2025-05-30T08:55:53.887Z"}}, {"model": "accounts.salesperson", "pk": 54, "fields": {"name": "رومانى رضا نصيف حنا", "employee_number": "70", "branch": 19, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-30T08:56:22.121Z", "updated_at": "2025-05-30T08:56:22.121Z"}}, {"model": "accounts.salesperson", "pk": 55, "fields": {"name": "احمد عزت حسن احمد شاكر", "employee_number": "72", "branch": 19, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-30T08:56:47.884Z", "updated_at": "2025-05-30T08:56:47.884Z"}}, {"model": "accounts.salesperson", "pk": 56, "fields": {"name": "مصطفى محمود زغلول كامل متولى", "employee_number": "283", "branch": 19, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-05-30T08:58:46.905Z", "updated_at": "2025-05-30T08:58:46.905Z"}}, {"model": "accounts.salesperson", "pk": 57, "fields": {"name": "يوس<PERSON> سعد", "employee_number": null, "branch": 14, "phone": "", "email": null, "address": "", "is_active": true, "notes": "", "created_at": "2025-07-23T12:55:00.564Z", "updated_at": "2025-07-23T12:56:08.155Z"}}, {"model": "accounts.contactformsettings", "pk": 1, "fields": {"title": "اتصل بنا", "description": null, "company_name": "Elkhawaga", "contact_email": "<EMAIL>", "contact_phone": "+20 ************", "contact_address": "مدينة نصر", "contact_hours": "9 صباحاً - 5 مساءً", "form_title": "نموذج الاتصال", "form_success_message": "تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.", "form_error_message": "يرجى ملء جميع الحقول المطلوبة."}}, {"model": "accounts.footersettings", "pk": 1, "fields": {"left_column_title": "Elkhawaga", "left_column_text": "نظام متكامل لإدارة العملاء والمبيعات والإنتاج والمخزون", "middle_column_title": "روابط سريعة", "right_column_title": "تواصل معنا"}}, {"model": "accounts.footersettings", "pk": 10, "fields": {"left_column_title": "عن الشركة", "left_column_text": "نظام متكامل لإدارة العملاء والمبيعات والإنتاج والمخزون", "middle_column_title": "روابط سريعة", "right_column_title": "تواصل معنا"}}, {"model": "accounts.aboutpagesettings", "pk": 1, "fields": {"title": "عن النظام", "subtitle": "نظام إدارة المصنع والعملاء", "system_version": "1.0.0", "system_release_date": "2025-04-30", "system_developer": "zakee tahawi", "system_description": "نظام متكامل لإدارة العملاء والمبيعات والإنتاج والمخزون"}}, {"model": "accounts.role", "pk": 1, "fields": {"name": "مدير النظام", "description": "الصلاحيات الكاملة على النظام", "is_system_role": true, "created_at": "2025-05-05T11:21:25.630Z", "updated_at": "2025-05-05T11:21:25.630Z", "permissions": []}}, {"model": "accounts.role", "pk": 2, "fields": {"name": "مدير", "description": "صلاحيات إدارية على معظم الميزات", "is_system_role": true, "created_at": "2025-05-05T11:21:25.640Z", "updated_at": "2025-05-05T11:21:25.640Z", "permissions": []}}, {"model": "accounts.role", "pk": 3, "fields": {"name": "مدير مبيعات", "description": "صلاحيات على المبيعات والعملاء", "is_system_role": true, "created_at": "2025-05-05T11:21:25.643Z", "updated_at": "2025-05-05T11:21:25.643Z", "permissions": []}}, {"model": "accounts.role", "pk": 4, "fields": {"name": "فني معاينة", "description": "صلاحيات تتعلق بالمعاينات", "is_system_role": true, "created_at": "2025-05-05T11:21:25.647Z", "updated_at": "2025-05-05T11:21:25.647Z", "permissions": []}}, {"model": "accounts.role", "pk": 5, "fields": {"name": "فني تركيب", "description": "صلاحيات تتعلق بالتركيبات", "is_system_role": true, "created_at": "2025-05-05T11:21:25.652Z", "updated_at": "2025-05-05T11:21:25.652Z", "permissions": []}}, {"model": "accounts.role", "pk": 6, "fields": {"name": "مسؤول المخزون", "description": "صلاحيات على المخزون والمنتجات", "is_system_role": true, "created_at": "2025-05-05T11:21:25.656Z", "updated_at": "2025-05-05T11:21:25.656Z", "permissions": []}}, {"model": "accounts.role", "pk": 7, "fields": {"name": "مسؤول المصنع", "description": "صلاحيات على المصنع والإنتاج", "is_system_role": true, "created_at": "2025-05-05T11:21:25.664Z", "updated_at": "2025-05-05T11:21:25.664Z", "permissions": []}}, {"model": "accounts.role", "pk": 8, "fields": {"name": "موظف مبيعات", "description": "صلاحيات محدودة للعمل على المبيعات", "is_system_role": true, "created_at": "2025-05-05T11:21:25.672Z", "updated_at": "2025-05-05T11:21:25.672Z", "permissions": []}}, {"model": "accounts.userrole", "pk": 1, "fields": {"user": 6, "role": 2, "assigned_at": "2025-06-21T16:36:41.812Z"}}, {"model": "accounts.systemsettings", "pk": 1, "fields": {"name": "نظام الخواجه", "currency": "EGP", "version": "1.0.0", "enable_notifications": true, "enable_email_notifications": false, "items_per_page": 20, "low_stock_threshold": 20, "enable_analytics": true, "maintenance_mode": false, "maintenance_message": "", "created_at": "2025-07-15T17:17:15.040Z", "updated_at": "2025-07-17T14:03:11.103Z"}}, {"model": "accounts.branchmessage", "pk": 3, "fields": {"branch": 16, "title": "☺️Welcome", "message": "اهلا بكم في فرع النزهة", "message_type": "announcement", "color": "primary", "icon": "fas fa-bell", "start_date": "2025-06-04T15:59:31Z", "end_date": "2025-06-04T21:00:00Z", "is_active": true, "created_at": "2025-06-04T15:59:34.214Z", "updated_at": "2025-06-04T16:35:59.950Z"}}, {"model": "odoo_db_manager.database", "pk": 1, "fields": {"name": "قاعدة البيانات الحالية (crm_system)", "db_type": "postgresql", "connection_info": {"HOST": "localhost", "NAME": "crm_system", "PORT": "5432", "USER": "postgres", "ENGINE": "django.db.backends.postgresql", "PASSWORD": "5525"}, "is_active": true, "status": true, "error_message": "", "created_at": "2025-07-15T17:18:20.233Z", "updated_at": "2025-07-24T11:59:30.508Z"}}, {"model": "odoo_db_manager.database", "pk": 3, "fields": {"name": "crm_system", "db_type": "postgresql", "connection_info": {"HOST": "localhost", "NAME": "crm_system", "PORT": "5432", "USER": "postgres", "ENGINE": "django.db.backends.postgresql", "PASSWORD": "5525"}, "is_active": true, "status": true, "error_message": "", "created_at": "2025-06-25T15:18:01.031Z", "updated_at": "2025-07-24T11:59:30.526Z"}}, {"model": "odoo_db_manager.backup", "pk": 3, "fields": {"database": 3, "name": "crm_system_full_20250724_145922", "file_path": "/home/<USER>/homeupdate/media/backups/crm_system_full_20250724_145922.json", "size": 215773, "backup_type": "full", "is_scheduled": false, "created_at": "2025-07-24T11:59:22.390Z", "created_by": 5}}, {"model": "odoo_db_manager.googledriveconfig", "pk": 1, "fields": {"name": "إعدادات Google Drive", "inspections_folder_id": "1FiMh_6elODXGCrWOyTNr7IHrto2y0Skv", "inspections_folder_name": "crm-insp", "contracts_folder_id": "", "contracts_folder_name": "العقود - Contracts", "credentials_file": "google_credentials/key_GIRscg6.json", "filename_pattern": "{customer}_{branch}_{date}_{order}", "is_active": true, "last_test": "2025-06-17T14:10:42.264Z", "test_status": "success", "test_message": "✅ تم الاتصال بنجاح مع Google Drive!\n\n🔗 Service Account: <EMAIL>\n📁 معرف المجلد: 1FiMh_6elODXGCrWOyTNr7IHrto2y0Skv\n✅ اختبار الرفع: نجح\n✅ صلاحيات الكتابة: متوفرة\n📄 ملف الاختبار: connection_test_20250617_141038.txt (تم حذفه)\n\n🎉 النظام جاهز لرفع ملفات المعاينات!", "total_uploads": 2, "last_upload": "2025-06-16T17:41:45.117Z", "created_at": "2025-06-11T09:24:05.670Z", "updated_at": "2025-06-11T09:25:17.089Z", "created_by": 5}}, {"model": "odoo_db_manager.restoreprogress", "pk": 1, "fields": {"session_id": "restore_1752599856272_amha65ku0", "user": null, "database": null, "filename": "download_6.GZ", "status": "completed", "total_items": 7143, "processed_items": 7143, "success_count": 7047, "error_count": 96, "current_step": "اكتملت العملية بنجاح", "progress_percentage": 100.0, "error_message": null, "result_data": {"errors": [{"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionissue']'", "index": 107, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionissue']'", "index": 108, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionissue']'", "index": 109, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionissue']'", "index": 110, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionline']'", "index": 111, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionline']'", "index": 112, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionline']'", "index": 113, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionline']'", "index": 114, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionorder']'", "index": 115, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionorder']'", "index": 116, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionorder']'", "index": 117, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionorder']'", "index": 118, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionstage']'", "index": 119, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionstage']'", "index": 120, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionstage']'", "index": 121, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['factory', 'productionstage']'", "index": 122, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['installations', 'installationissue']'", "index": 143, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['installations', 'installationissue']'", "index": 144, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['installations', 'installationissue']'", "index": 145, "model": "auth.permission"}, {"pk": "<PERSON>ي<PERSON> محدد", "error": "ContentType matching query does not exist.: (auth.permission:pk=None) field_value was '['installations', 'installationissue']'", "index": 146, "model": "auth.permission"}], "summary": "تم استعادة 7047 من 7143 عنصر بنسبة نجاح 98.7%", "filename": "tmpch0nbfi2.json", "error_count": 96, "total_items": 7143, "success_rate": 98.65602687946242, "success_count": 7047, "is_comprehensive": true, "foreign_keys_handled": true}, "created_at": "2025-07-15T17:17:36.312Z", "updated_at": "2025-07-15T17:18:20.235Z"}}, {"model": "odoo_db_manager.googlesyncconfig", "pk": 1, "fields": {"name": "مزامنة غوغل الرئيسية", "spreadsheet_id": "1kL2BGYgSdfCZSa4LTcCJHfOFT_keoIiYm7l7l9vcU0c", "credentials_file": "google_credentials/google_credentials/key.json", "is_active": true, "last_sync": "2025-06-11T17:01:09.279Z", "sync_frequency": 1, "created_at": "2025-06-10T09:38:13.395Z", "updated_at": "2025-07-22T17:39:21.835Z", "sync_databases": true, "sync_users": true, "sync_customers": true, "sync_orders": true, "sync_products": true, "sync_settings": true, "sync_inspections": true}}, {"model": "odoo_db_manager.googlesynclog", "pk": 1, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "factory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المصانع: cannot import name 'Factory' from 'factory.models' (C:\\Users\\<USER>\\Desktop\\crm\\factory\\models.py)"}, "reviews": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المعاينات: No module named 'reviews'"}, "vendors": {"status": "error", "message": "حدث خطأ أثناء مزامنة البائعين: No module named 'vendors'"}, "branches": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة الفروع: No module named 'branches'"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "error", "message": "حدث خطأ أثناء مزامنة الإعدادات: App 'accounts' doesn't have a 'Setting' model."}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inventory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المخزون: cannot import name 'InventoryItem' from 'inventory.models' (C:\\Users\\<USER>\\Desktop\\crm\\inventory\\models.py)"}, "installations": {"status": "success", "message": "تمت مزامنة 0 تركيب"}}, "created_at": "2025-06-10T09:39:44.043Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 2, "fields": {"config": 1, "status": "error", "message": "حدث خطأ أثناء المزامنة: name 'sync_products' is not defined", "details": {}, "created_at": "2025-06-10T09:48:30.976Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 3, "fields": {"config": 1, "status": "error", "message": "حدث خطأ أثناء المزامنة: name 'sync_products' is not defined", "details": {}, "created_at": "2025-06-10T09:59:49.083Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 4, "fields": {"config": 1, "status": "error", "message": "حدث خطأ أثناء المزامنة: name 'sync_products' is not defined", "details": {}, "created_at": "2025-06-10T10:04:36.241Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 5, "fields": {"config": 1, "status": "error", "message": "حدث خطأ أثناء المزامنة: name 'sync_products' is not defined", "details": {}, "created_at": "2025-06-10T10:05:40.315Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 6, "fields": {"config": 1, "status": "error", "message": "حدث خطأ أثناء المزامنة: name 'sync_settings' is not defined", "details": {}, "created_at": "2025-06-10T10:08:05.561Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 7, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "factory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المصنع: cannot import name 'Production' from 'factory.models' (C:\\Users\\<USER>\\Desktop\\crm\\factory\\models.py)"}, "reviews": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المعاينات: Invalid field name(s) given in select_related: 'technician'. Choices are: customer, branch, inspector, responsible_employee, created_by, order"}, "vendors": {"status": "success", "message": "تمت مزامنة 53 بائع"}, "branches": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة الفروع: 'Branch' object has no attribute 'created_at'"}, "products": {"status": "error", "message": "حدث خطأ أثناء مزامنة المنتجات: Cannot find 'suppliers' on Product object, 'suppliers' is an invalid parameter to prefetch_related()"}, "settings": {"status": "error", "message": "حدث خطأ أثناء مزامنة الإعدادات: 'CompanyInfo' object has no attribute 'updated_at'"}, "customers": {"status": "error", "message": "حدث خطأ أثناء مزامنة العملاء: Cannot find 'orders' on Customer object, 'orders' is an invalid parameter to prefetch_related()"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inventory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المخزون: Cannot find 'suppliers' on Product object, 'suppliers' is an invalid parameter to prefetch_related()"}, "installations": {"status": "error", "message": "حدث خطأ أثناء مزامنة التركيبات: Invalid field name(s) given in select_related: 'customer', 'technician', 'branch'. Choices are: order, inspection, team, created_by"}}, "created_at": "2025-06-10T10:16:50.730Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 8, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "factory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المصنع: cannot import name 'Production' from 'factory.models' (C:\\Users\\<USER>\\Desktop\\crm\\factory\\models.py)"}, "reviews": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المعاينات: Invalid field name(s) given in select_related: 'technician'. Choices are: customer, branch, inspector, responsible_employee, created_by, order"}, "vendors": {"status": "success", "message": "تمت مزامنة 53 بائع"}, "branches": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة الفروع: 'Branch' object has no attribute 'created_at'"}, "products": {"status": "error", "message": "حدث خطأ أثناء مزامنة المنتجات: Cannot find 'suppliers' on Product object, 'suppliers' is an invalid parameter to prefetch_related()"}, "settings": {"status": "error", "message": "حدث خطأ أثناء مزامنة الإعدادات: 'CompanyInfo' object has no attribute 'updated_at'"}, "customers": {"status": "error", "message": "حدث خطأ أثناء مزامنة العملاء: Cannot find 'orders' on Customer object, 'orders' is an invalid parameter to prefetch_related()"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inventory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المخزون: Cannot find 'suppliers' on Product object, 'suppliers' is an invalid parameter to prefetch_related()"}, "installations": {"status": "error", "message": "حدث خطأ أثناء مزامنة التركيبات: Invalid field name(s) given in select_related: 'customer', 'technician', 'branch'. Choices are: order, inspection, team, created_by"}}, "created_at": "2025-06-10T10:18:46.542Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 9, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "factory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المصنع: cannot import name 'Production' from 'factory.models' (C:\\Users\\<USER>\\Desktop\\crm\\factory\\models.py)"}, "reviews": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المعاينات: Invalid field name(s) given in select_related: 'technician'. Choices are: customer, branch, inspector, responsible_employee, created_by, order"}, "vendors": {"status": "success", "message": "تمت مزامنة 53 بائع"}, "branches": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة الفروع: 'Branch' object has no attribute 'created_at'"}, "products": {"status": "error", "message": "حدث خطأ أثناء مزامنة المنتجات: Cannot find 'suppliers' on Product object, 'suppliers' is an invalid parameter to prefetch_related()"}, "settings": {"status": "error", "message": "حدث خطأ أثناء مزامنة الإعدادات: 'CompanyInfo' object has no attribute 'updated_at'"}, "customers": {"status": "error", "message": "حدث خطأ أثناء مزامنة العملاء: Cannot find 'orders' on Customer object, 'orders' is an invalid parameter to prefetch_related()"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات بنجاح"}, "inventory": {"status": "error", "message": "حدث خطأ أثناء مزامنة المخزون: Cannot find 'suppliers' on Product object, 'suppliers' is an invalid parameter to prefetch_related()"}, "installations": {"status": "error", "message": "حدث خطأ أثناء مزامنة التركيبات: Invalid field name(s) given in select_related: 'customer', 'branch', 'technician'. Choices are: order, inspection, team, created_by"}}, "created_at": "2025-06-10T15:51:19.611Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 10, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "error", "message": "حدث خطأ أثناء مزامنة الإعدادات: App 'accounts' doesn't have a 'Setting' model."}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}}, "created_at": "2025-06-10T16:48:34.527Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 11, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "error", "message": "حدث خطأ أثناء مزامنة الإعدادات: App 'accounts' doesn't have a 'Setting' model."}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}}, "created_at": "2025-06-11T08:23:40.236Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 12, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 0 معاينة"}}, "created_at": "2025-06-11T08:54:39.666Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 13, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 0 معاينة"}}, "created_at": "2025-06-11T08:55:25.394Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 14, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 0 معاينة"}}, "created_at": "2025-06-11T08:59:25.039Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 15, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 0 معاينة"}}, "created_at": "2025-06-11T08:59:31.811Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 16, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 0 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 0 معاينة"}}, "created_at": "2025-06-11T09:09:43.850Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 17, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المعاينات: 'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T09:34:17.797Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 18, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المعاينات: 'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T09:40:23.518Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 19, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T09:44:23.808Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 20, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T09:47:30.363Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 21, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T09:54:09.282Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 22, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T09:54:23.359Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 23, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T10:05:04.313Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 24, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T10:12:33.821Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 25, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T10:28:20.465Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 26, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T10:33:52.654Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 27, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T10:38:20.710Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 28, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T10:39:11.534Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 29, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "error", "message": "حد<PERSON> خطأ أثناء مزامنة المنتجات: 'Product' object has no attribute 'quantity'"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T10:39:26.734Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 30, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 343 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T10:45:43.499Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 31, "fields": {"config": 1, "status": "error", "message": "حدث خطأ أثناء المزامنة: name 'sync_databases' is not defined", "details": {}, "created_at": "2025-06-11T10:52:30.721Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 32, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 343 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "error", "message": "'User' object has no attribute 'name'"}}, "created_at": "2025-06-11T10:54:28.155Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 33, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 343 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T11:01:51.530Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 34, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 343 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T11:06:30.243Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 35, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 343 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T11:11:38.039Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 36, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 1 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 343 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 1 معاينة"}}, "created_at": "2025-06-11T11:16:46.880Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 37, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 2 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 344 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 2 معاينة"}}, "created_at": "2025-06-11T11:23:44.364Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 38, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 2 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 344 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 2 معاينة"}}, "created_at": "2025-06-11T12:38:45.227Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 39, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 2 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 344 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 2 معاينة"}}, "created_at": "2025-06-11T16:12:28.735Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 40, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 2 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 344 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 2 معاينة"}}, "created_at": "2025-06-11T16:42:30.379Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 41, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 2 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 344 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 2 معاينة"}}, "created_at": "2025-06-11T16:48:31.795Z"}}, {"model": "odoo_db_manager.googlesynclog", "pk": 42, "fields": {"config": 1, "status": "success", "message": "تمت المزامنة مع Google Sheets بنجاح", "details": {"users": {"status": "success", "message": "تمت مزامنة 6 مستخدم"}, "orders": {"status": "success", "message": "تمت مزامنة 2 طلب"}, "products": {"status": "success", "message": "تمت مزامنة 344 منتج"}, "settings": {"status": "success", "message": "تمت مزامنة إعدادات الشركة والنظام بنجاح"}, "customers": {"status": "success", "message": "تمت مزامنة 19 عميل"}, "databases": {"status": "success", "message": "تمت مزامنة 2 قاعدة بيانات"}, "inspections": {"status": "success", "message": "تمت مزامنة 2 معاينة"}}, "created_at": "2025-06-11T17:01:09.288Z"}}]