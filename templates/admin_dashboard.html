{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}داش بورد الإدارة المحسن - نظام الخواجه{% endblock %}

{% block extra_css %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Custom Dashboard CSS -->
<link rel="stylesheet" href="{% static 'css/admin-dashboard.css' %}">
<style>
    .dashboard-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        color: white;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }
    
    .dashboard-card.customers {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .dashboard-card.orders {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .dashboard-card.manufacturing {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .dashboard-card.inspections {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .dashboard-card.installations {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .dashboard-card.inventory {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .stat-icon {
        font-size: 3rem;
        opacity: 0.8;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .comparison-card {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .metric-item {
        background: white;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .metric-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    
    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }
    
    .metric-label {
        font-size: 0.9rem;
        color: #666;
    }
    
    .trend-indicator {
        font-size: 0.8rem;
        padding: 2px 8px;
        border-radius: 12px;
        margin-left: 10px;
    }
    
    .trend-up {
        background: #d4edda;
        color: #155724;
    }
    
    .trend-down {
        background: #f8d7da;
        color: #721c24;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .dashboard-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .dashboard-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }
    
    .filter-form {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: end;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        min-width: 150px;
    }
    
    .filter-label {
        font-weight: bold;
        margin-bottom: 5px;
        color: #333;
    }
    
    .filter-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        background: white;
        transition: border-color 0.3s ease;
    }
    
    .filter-select:focus {
        border-color: #667eea;
        outline: none;
    }
    
    .apply-filters-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .apply-filters-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    
    .year-mode-toggle {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s ease;
        margin-left: 10px;
    }
    
    .year-mode-toggle:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(67, 233, 123, 0.4);
    }
    
    .performance-metrics {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .performance-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
    
    .performance-item {
        text-align: center;
        padding: 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
    }
    
    .performance-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .performance-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    @media (max-width: 768px) {
        .dashboard-title {
            font-size: 2rem;
        }
        
        .stat-number {
            font-size: 2rem;
        }
        
        .filter-form {
            flex-direction: column;
        }
        
        .filter-group {
            min-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-chart-line me-3"></i>
            داش بورد الإدارة المحسن
        </h1>
        <p class="dashboard-subtitle">
            تحليلات شاملة لجميع أقسام النظام - 
            {% if show_all_years %}
                جميع السنوات المتاحة
            {% elif selected_year == 'all' %}
                جميع السنوات
            {% elif selected_month == 'year' %}
                سنة {{ selected_year|default:timezone.now.year }}
            {% else %}
                {{ selected_month|default:timezone.now.month }}/{{ selected_year|default:timezone.now.year }}
            {% endif %}
        </p>
    </div>

    <!-- Performance Metrics -->
    {% if performance_metrics %}
    <div class="performance-metrics">
        <h4 class="mb-3">
            <i class="fas fa-tachometer-alt me-2"></i>
            مؤشرات الأداء الرئيسية
        </h4>
        <div class="performance-grid">
            <div class="performance-item">
                <div class="performance-value">{{ performance_metrics.completion_rate }}%</div>
                <div class="performance-label">معدل إتمام الطلبات</div>
            </div>
            <div class="performance-item">
                <div class="performance-value">{{ performance_metrics.inspection_success_rate }}%</div>
                <div class="performance-label">معدل نجاح المعاينات</div>
            </div>
            <div class="performance-item">
                <div class="performance-value">{{ performance_metrics.low_stock_rate }}%</div>
                <div class="performance-label">معدل المخزون المنخفض</div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Filters Section -->
    <div class="filter-section">
        <h4 class="mb-3">
            <i class="fas fa-filter me-2"></i>
            فلاتر البيانات
        </h4>
        <form method="GET" class="filter-form" id="dashboard-filters">
            <div class="filter-group">
                <label class="filter-label">الف��ع:</label>
                <select name="branch" class="filter-select" id="branch-filter">
                    <option value="all" {% if selected_branch == 'all' %}selected{% endif %}>جميع الفروع</option>
                    {% for branch in branches %}
                        <option value="{{ branch.id }}" {% if selected_branch == branch.id|stringformat:"s" %}selected{% endif %}>
                            {{ branch.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">الفترة:</label>
                <select name="month" class="filter-select" id="period-select">
                    <option value="year" {% if selected_month == 'year' %}selected{% endif %}>السنة الكاملة</option>
                    {% for month_num, month_name in months %}
                        <option value="{{ month_num }}" {% if selected_month == month_num %}selected{% endif %}>
                            {{ month_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">السنة:</label>
                <select name="year" class="filter-select" id="year-filter">
                    {% if show_all_years %}
                        <option value="all" {% if selected_year == 'all' %}selected{% endif %}>
                            جميع السنوات
                        </option>
                        {% for year in all_years %}
                            <option value="{{ year }}" {% if selected_year == year %}selected{% endif %}>
                                {{ year }}
                            </option>
                        {% endfor %}
                    {% else %}
                        {% for year in years %}
                            <option value="{{ year }}" {% if selected_year == year %}selected{% endif %}>
                                {{ year }}
                            </option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">نوع المقارنة:</label>
                <select name="comparison_type" class="filter-select" id="comparison-type-select">
                    <option value="month" {% if comparison_type == 'month' %}selected{% endif %}>مقارنة شهرية</option>
                    <option value="year" {% if comparison_type == 'year' %}selected{% endif %}>مقارنة سنوية</option>
                </select>
            </div>
            
            <div class="filter-group" id="comparison-month-group">
                <label class="filter-label">مقارنة مع الشهر:</label>
                <select name="comparison_month" class="filter-select" id="comparison-month-filter">
                    <option value="">بدون مقارنة</option>
                    {% for month_num, month_name in months %}
                        <option value="{{ month_num }}" {% if comparison_month == month_num %}selected{% endif %}>
                            {{ month_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">سنة المقارنة:</label>
                <select name="comparison_year" class="filter-select" id="comparison-year-filter">
                    <option value="">بدون مقارنة</option>
                    {% if show_all_years %}
                        {% for year in all_years %}
                            <option value="{{ year }}" {% if comparison_year == year %}selected{% endif %}>
                                {{ year }}
                            </option>
                        {% endfor %}
                    {% else %}
                        {% for year in years %}
                            <option value="{{ year }}" {% if comparison_year == year %}selected{% endif %}>
                                {{ year }}
                            </option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
            
            <input type="hidden" name="show_all_years" value="{{ show_all_years|yesno:'true,false' }}" id="show-all-years-input">
            
            <button type="submit" class="apply-filters-btn" id="apply-filters-btn">
                <i class="fas fa-search me-2"></i>
                تطبيق الفلاتر
            </button>
            
            <button type="button" class="year-mode-toggle" id="year-mode-toggle">
                <i class="fas fa-calendar-alt me-2"></i>
                {% if show_all_years %}
                    السنوات المحددة فقط
                {% else %}
                    عرض جميع السنوات
                {% endif %}
            </button>
        </form>
        
        <!-- عرض الفلاتر المطبقة -->
        <div class="mt-3 p-3 bg-light rounded">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                الفلاتر المطبقة: 
                <strong>الفرع:</strong> 
                {% if selected_branch == 'all' %}
                    جميع الفروع
                {% else %}
                    {% for branch in branches %}
                        {% if branch.id|stringformat:"s" == selected_branch %}
                            {{ branch.name }}
                        {% endif %}
                    {% endfor %}
                {% endif %}
                | 
                <strong>الفترة:</strong> 
                {% if show_all_years %}
                    جميع السنوات المتاحة
                {% elif selected_year == 'all' %}
                    جميع السنوات
                {% elif selected_month == 'year' %}
                    السنة الكاملة {{ selected_year }}
                {% else %}
                    {% for month_num, month_name in months %}
                        {% if month_num == selected_month %}
                            {{ month_name }} {{ selected_year }}
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </small>
        </div>
    </div>

    <!-- Main Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card dashboard-card customers h-100">
                <div class="card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number">{{ customers_stats.total|default:0 }}</div>
                    <div class="stat-label">إجمالي العملاء</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card dashboard-card orders h-100">
                <div class="card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-number">{{ orders_stats.total|default:0 }}</div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card dashboard-card manufacturing h-100">
                <div class="card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="stat-number">{{ manufacturing_stats.total|default:0 }}</div>
                    <div class="stat-label">أوامر التصنيع</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card dashboard-card inspections h-100">
                <div class="card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="stat-number">{{ inspections_stats.total|default:0 }}</div>
                    <div class="stat-label">المعاينات</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card dashboard-card installations h-100">
                <div class="card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="stat-number">{{ installation_orders_stats.total|default:0 }}</div>
                    <div class="stat-label">طلبات التركيب</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card dashboard-card inventory h-100">
                <div class="card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-number">{{ inventory_stats.total_products|default:0 }}</div>
                    <div class="stat-label">المنتجات</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Summary -->
    {% if dashboard_summary %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    ملخص الداشبورد
                </h5>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ dashboard_summary.total_revenue|floatformat:0|intcomma }}</div>
                        <div class="metric-label">إجمالي الإيرادات (ج.م)</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-primary">{{ dashboard_summary.total_orders }}</div>
                        <div class="metric-label">إجمالي الطلبات</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-info">{{ dashboard_summary.total_customers }}</div>
                        <div class="metric-label">إجمالي العملاء</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ dashboard_summary.avg_order_value|floatformat:0|intcomma }}</div>
                        <div class="metric-label">متوسط قيمة الطلب (ج.م)</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Detailed Statistics -->
    <div class="row">
        <!-- Customers Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-users me-2"></i>
                    إحصائيات العملاء
                </h5>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ customers_stats.active|default:0 }}</div>
                        <div class="metric-label">عملاء نشطون</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ customers_stats.inactive|default:0 }}</div>
                        <div class="metric-label">عملاء غير نشطين</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-info">{{ customers_stats.new_this_month|default:0 }}</div>
                        <div class="metric-label">عملاء جدد هذا الشهر</div>
                    </div>
                    {% if customers_stats.growth_rate %}
                    <div class="metric-item">
                        <div class="metric-value {% if customers_stats.growth_rate > 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ customers_stats.growth_rate }}%
                        </div>
                        <div class="metric-label">معدل النمو الشهري</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Orders Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-shopping-cart me-2"></i>
                    إحصائيات الطلبات
                </h5>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ orders_stats.pending|default:0 }}</div>
                        <div class="metric-label">قيد الانتظار</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-info">{{ orders_stats.in_progress|default:0 }}</div>
                        <div class="metric-label">قيد التنفيذ</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ orders_stats.completed|default:0 }}</div>
                        <div class="metric-label">مكتملة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-primary">{{ orders_stats.total_amount|floatformat:0|intcomma }}</div>
                        <div class="metric-label">إجمالي المبيعات (ج.م)</div>
                    </div>
                    {% if orders_stats.completion_rate %}
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ orders_stats.completion_rate }}%</div>
                        <div class="metric-label">معدل الإتمام</div>
                    </div>
                    {% endif %}
                    {% if orders_stats.avg_amount %}
                    <div class="metric-item">
                        <div class="metric-value text-info">{{ orders_stats.avg_amount|floatformat:0|intcomma }}</div>
                        <div class="metric-label">متوسط قيمة الطلب (ج.م)</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Manufacturing Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-industry me-2"></i>
                    إحصائيات التصنيع
                </h5>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ manufacturing_stats.pending|default:0 }}</div>
                        <div class="metric-label">قيد الانتظار</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-info">{{ manufacturing_stats.in_progress|default:0 }}</div>
                        <div class="metric-label">قيد التصنيع</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ manufacturing_stats.completed|default:0 }}</div>
                        <div class="metric-label">مكتملة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-primary">{{ manufacturing_stats.delivered|default:0 }}</div>
                        <div class="metric-label">تم التسليم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inspections Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-clipboard-check me-2"></i>
                    إحصائيات المعاينات
                </h5>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ inspections_stats.pending|default:0 }}</div>
                        <div class="metric-label">قيد الانتظار</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-info">{{ inspections_stats.scheduled|default:0 }}</div>
                        <div class="metric-label">مجدولة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ inspections_stats.completed|default:0 }}</div>
                        <div class="metric-label">مكتملة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ inspections_stats.successful|default:0 }}</div>
                        <div class="metric-label">ناجحة</div>
                    </div>
                    {% if inspections_stats.success_rate %}
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ inspections_stats.success_rate }}%</div>
                        <div class="metric-label">معدل النجاح</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Installations Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-tools me-2"></i>
                    إحصائيات التركيبات
                </h5>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ installations_stats.pending|default:0 }}</div>
                        <div class="metric-label">قيد الانتظار</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-info">{{ installations_stats.scheduled|default:0 }}</div>
                        <div class="metric-label">مجدولة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-primary">{{ installations_stats.in_installation|default:0 }}</div>
                        <div class="metric-label">قيد التركيب</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ installations_stats.completed|default:0 }}</div>
                        <div class="metric-label">مكتملة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-boxes me-2"></i>
                    إحصائيات المخزون
                </h5>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value text-primary">{{ inventory_stats.total_products|default:0 }}</div>
                        <div class="metric-label">إجمالي المنتجات</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ inventory_stats.low_stock|default:0 }}</div>
                        <div class="metric-label">منتجات منخفضة المخزون</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-danger">{{ inventory_stats.out_of_stock|default:0 }}</div>
                        <div class="metric-label">منتجات نفذت</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value text-success">{{ inventory_stats.total_value|floatformat:0|intcomma }}</div>
                        <div class="metric-label">قيمة المخزون (ج.م)</div>
                    </div>
                    {% if inventory_stats.low_stock_percentage %}
                    <div class="metric-item">
                        <div class="metric-value text-warning">{{ inventory_stats.low_stock_percentage }}%</div>
                        <div class="metric-label">نسبة المخزون المنخفض</div>
                    </div>
                    {% endif %}
                    {% if inventory_stats.out_of_stock_percentage %}
                    <div class="metric-item">
                        <div class="metric-value text-danger">{{ inventory_stats.out_of_stock_percentage }}%</div>
                        <div class="metric-label">نسبة المنتجات النافذة</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Section -->
    {% if comparison_data %}
    <div class="comparison-card">
        <h4 class="mb-3">
            <i class="fas fa-chart-bar me-2"></i>
            {% if comparison_type == 'year' %}
                مقارنة مع سنة {{ comparison_year }}
            {% else %}
                مقارنة مع {{ comparison_month }}/{{ comparison_year }}
            {% endif %}
        </h4>
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="metric-item">
                    <div class="metric-value">
                        {{ customers_stats.total|default:0 }}
                        {% if comparison_data.customers.total %}
                            <span class="trend-indicator {% if customers_stats.total > comparison_data.customers.total %}trend-up{% else %}trend-down{% endif %}">
                                {% if customers_stats.total > comparison_data.customers.total %}↑{% else %}↓{% endif %}
                            </span>
                        {% endif %}
                    </div>
                    <div class="metric-label">العملاء</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="metric-item">
                    <div class="metric-value">
                        {{ orders_stats.total|default:0 }}
                        {% if comparison_data.orders.total %}
                            <span class="trend-indicator {% if orders_stats.total > comparison_data.orders.total %}trend-up{% else %}trend-down{% endif %}">
                                {% if orders_stats.total > comparison_data.orders.total %}↑{% else %}↓{% endif %}
                            </span>
                        {% endif %}
                    </div>
                    <div class="metric-label">الطلبات</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="metric-item">
                    <div class="metric-value">
                        {{ manufacturing_stats.total|default:0 }}
                        {% if comparison_data.manufacturing.total %}
                            <span class="trend-indicator {% if manufacturing_stats.total > comparison_data.manufacturing.total %}trend-up{% else %}trend-down{% endif %}">
                                {% if manufacturing_stats.total > comparison_data.manufacturing.total %}↑{% else %}↓{% endif %}
                            </span>
                        {% endif %}
                    </div>
                    <div class="metric-label">التصنيع</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="metric-item">
                    <div class="metric-value">
                        {{ inspections_stats.total|default:0 }}
                        {% if comparison_data.inspections.total %}
                            <span class="trend-indicator {% if inspections_stats.total > comparison_data.inspections.total %}trend-up{% else %}trend-down{% endif %}">
                                {% if inspections_stats.total > comparison_data.inspections.total %}↑{% else %}↓{% endif %}
                            </span>
                        {% endif %}
                    </div>
                    <div class="metric-label">المعاينات</div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Charts Section -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    {% if chart_data.type == 'yearly' %}
                        الطلبات السنوية
                    {% else %}
                        الطلبات الشهرية - {{ selected_year }}
                    {% endif %}
                </h5>
                <canvas id="ordersChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    {% if chart_data.type == 'yearly' %}
                        العملاء السنويون
                    {% else %}
                        العملاء الشهريون - {{ selected_year }}
                    {% endif %}
                </h5>
                <canvas id="customersChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<script>
// Chart.js configuration
{% if chart_data.type == 'yearly' %}
    const labels = [{% for item in chart_data.orders_by_year %}'{{ item.year }}'{% if not forloop.last %}, {% endif %}{% endfor %}];
    const ordersData = [{% for item in chart_data.orders_by_year %}{{ item.count }}{% if not forloop.last %}, {% endif %}{% endfor %}];
    const customersData = [{% for item in chart_data.customers_by_year %}{{ item.count }}{% if not forloop.last %}, {% endif %}{% endfor %}];
{% else %}
    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
                   'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    const labels = months;
    const ordersData = [{% for item in chart_data.orders_by_month %}{{ item.count }}{% if not forloop.last %}, {% endif %}{% endfor %}];
    const customersData = [{% for item in chart_data.customers_by_month %}{{ item.count }}{% if not forloop.last %}, {% endif %}{% endfor %}];
{% endif %}

// Orders Chart
const ordersCtx = document.getElementById('ordersChart').getContext('2d');
const ordersChart = new Chart(ordersCtx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: 'الطلبات',
            data: ordersData,
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Customers Chart
const customersCtx = document.getElementById('customersChart').getContext('2d');
const customersChart = new Chart(customersCtx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: 'العملاء',
            data: customersData,
            borderColor: '#f093fb',
            backgroundColor: 'rgba(240, 147, 251, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Enhanced Dashboard Functionality
document.addEventListener('DOMContentLoaded', function() {
    const comparisonTypeSelect = document.getElementById('comparison-type-select');
    const comparisonMonthGroup = document.getElementById('comparison-month-group');
    const yearModeToggle = document.getElementById('year-mode-toggle');
    const showAllYearsInput = document.getElementById('show-all-years-input');
    const filterForm = document.getElementById('dashboard-filters');
    const applyBtn = document.getElementById('apply-filters-btn');
    
    // Comparison Type Control
    function toggleComparisonMonth() {
        if (comparisonTypeSelect.value === 'year') {
            comparisonMonthGroup.style.display = 'none';
        } else {
            comparisonMonthGroup.style.display = 'block';
        }
    }
    
    // Initial state
    toggleComparisonMonth();
    
    // Listen for changes
    comparisonTypeSelect.addEventListener('change', toggleComparisonMonth);
    
    // Year Mode Toggle
    yearModeToggle.addEventListener('click', function() {
        const currentValue = showAllYearsInput.value === 'true';
        showAllYearsInput.value = currentValue ? 'false' : 'true';
        filterForm.submit();
    });
    
    // Enhanced Filter Functionality
    if (filterForm && applyBtn) {
        // Add loading state to submit button
        applyBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التطبيق...';
            this.disabled = true;
            this.style.opacity = '0.7';
            
            // Submit form after short delay
            setTimeout(() => {
                filterForm.submit();
            }, 300);
        });
        
        // Auto-submit when period changes to "year"
        const periodSelect = document.getElementById('period-select');
        if (periodSelect) {
            periodSelect.addEventListener('change', function() {
                if (this.value === 'year') {
                    console.log('Auto-submitting filters for year selection');
                    filterForm.submit();
                }
            });
        }
        
        // Add visual feedback for filter changes
        const filterSelects = filterForm.querySelectorAll('select');
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                // Add visual feedback
                this.style.borderColor = '#667eea';
                this.style.boxShadow = '0 0 0 0.2rem rgba(102, 126, 234, 0.25)';
                
                setTimeout(() => {
                    this.style.borderColor = '#e9ecef';
                    this.style.boxShadow = 'none';
                }, 1000);
                
                console.log(`Filter changed: ${this.name} = ${this.value}`);
            });
        });
    }
    
    // Debug logging
    console.log('Enhanced Dashboard initialized');
    console.log('Current filters:', {
        branch: document.getElementById('branch-filter')?.value,
        month: document.getElementById('period-select')?.value,
        year: document.getElementById('year-filter')?.value,
        show_all_years: showAllYearsInput?.value,
        comparison_type: document.getElementById('comparison-type-select')?.value
    });
});
</script>
{% endblock %}