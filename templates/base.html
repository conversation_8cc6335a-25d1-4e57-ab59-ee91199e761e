<!DOCTYPE html>
{% load static %}
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة العملاء{% endblock %}</title>
    
    <!-- Favicon -->
    {% if company_info.logo %}
        <link rel="icon" type="image/png" href="{{ company_info.logo.url }}">
        <link rel="shortcut icon" type="image/png" href="{{ company_info.logo.url }}">
        <link rel="apple-touch-icon" href="{{ company_info.logo.url }}">
    {% else %}
        <link rel="icon" type="image/png" href="{% static 'images/favicon.png' %}">
        <link rel="shortcut icon" type="image/png" href="{% static 'images/favicon.png' %}">
        <link rel="apple-touch-icon" href="{% static 'images/favicon.png' %}">
    {% endif %}

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Animate.css for SweetAlert animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
    
    <!-- Global Cairo Font CSS -->
    <link rel="stylesheet" href="{% static 'css/global-cairo-font.css' %}">
    
    <!-- Font Awesome Fix CSS -->
    <link rel="stylesheet" href="{% static 'css/font-awesome-fix.css' %}">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- Modern Black Theme CSS -->
    <link rel="stylesheet" href="{% static 'css/modern-black-theme.css' %}">
    
    <!-- Modern Black Theme Fixes -->
    <link rel="stylesheet" href="{% static 'css/modern-black-fixes.css' %}">
      <!-- Extra Dark Mode Fixes -->
    <link rel="stylesheet" href="{% static 'css/extra-dark-fixes.css' %}">
    
    <!-- Custom Theme Enhancements -->
    <link rel="stylesheet" href="{% static 'css/custom-theme-enhancements.css' %}">

    <!-- Unified Status System CSS -->
    <link rel="stylesheet" href="{% static 'css/unified-status-system.css' %}">
    
    <!-- Unified Status Colors CSS -->
    <link rel="stylesheet" href="{% static 'css/unified-status-colors.css' %}">

    <!-- Responsive Footer CSS -->
    <link rel="stylesheet" href="{% static 'css/responsive-footer.css' %}">

    <!-- Anti-Flicker CSS -->
    <link rel="stylesheet" href="{% static 'css/anti-flicker.css' %}">

    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Google Fonts - Cairo (Arabic) -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Google Fonts - Roboto (English) -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Database Card CSS -->
    {% if 'database' in request.path %}
    <link rel="stylesheet" href="{% static 'css/db_card.css' %}">
    {% endif %}    <!-- تطبيق فوري للثيم لتجنب الوميض -->
    <script>
        // تطبيق الثيم فوراً قبل رسم الصفحة - محسن لتجنب الوميض
        (function() {
            // إخفاء body مؤقتاً لتجنب الوميض
            document.documentElement.style.visibility = 'hidden';
            
            {% if user.is_authenticated %}
                var userDefaultTheme = '{{ user.default_theme }}';
                // Always prefer user's default theme from database after login
                if (userDefaultTheme && userDefaultTheme !== 'default') {
                    document.documentElement.setAttribute('data-theme', userDefaultTheme);
                    localStorage.setItem('selectedTheme', userDefaultTheme);
                    if (document.getElementById('themeSelector')) {
                        document.getElementById('themeSelector').value = userDefaultTheme;
                    }
                } else {
                    var savedTheme = localStorage.getItem('selectedTheme');
                    // Only use localStorage theme if no default theme is set
                    if (savedTheme && savedTheme !== 'default') {
                        document.documentElement.setAttribute('data-theme', savedTheme);
                    }
                }
            {% else %}
                // For non-authenticated users, use localStorage theme
                var savedTheme = localStorage.getItem('selectedTheme');
                if (savedTheme && savedTheme !== 'default') {
                    document.documentElement.setAttribute('data-theme', savedTheme);
                }
            {% endif %}
            
            // إظهار الصفحة بعد تطبيق الثيم
            setTimeout(function() {
                document.documentElement.style.visibility = 'visible';
            }, 10);
        })();
    </script>

    <!-- CSS فوري للثيم الافتراضي لتجنب الوميض -->
    <style>
        /* CSS فوري للثيم الافتراضي - الأيقونات فوق النصوص في navbar */
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            text-align: center !important;
            padding: 0.6rem 1rem !important;
            min-height: 65px !important;
            justify-content: center !important;
            line-height: 1.1 !important;
            gap: 0.3rem !important;
        }
        
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link i,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .fas,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .far,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .fab {
            font-size: 1.4rem !important;
            margin: 0 !important;
            display: block !important;
            order: 1 !important;
            width: 100% !important;
            text-align: center !important;
        }
        
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .nav-text,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link span {
            order: 2 !important;
            font-size: 0.8rem !important;
            display: block !important;
            width: 100% !important;
            text-align: center !important;
            margin: 0 !important;
        }
    </style>

    {% block extra_css %}{% endblock %}

    <!-- 🎨 CSS الإشعارات الإبداعي والجميل -->
    <style>
        /* صندوق الإشعارات الرئيسي */
        .notification-container {
            display: flex;
            align-items: center;
            position: relative;
        }

        /* جرس الإشعارات */
        .notification-bell, .complaint-bell {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notification-bell:hover, .complaint-bell:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .notification-bell i, .complaint-bell i {
            font-size: 18px;
            color: #fff;
        }

        /* شارات العدد */
        .notification-badge, .complaint-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: linear-gradient(45deg, #0d6efd, #6f42c1);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 11px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: bounce 2s infinite;
            box-shadow: 0 2px 8px rgba(13, 110, 253, 0.4);
        }

        .complaint-badge {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
        }

        /* نبضة الإشعار */
        .notification-pulse, .complaint-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(13, 110, 253, 0.3);
            animation: pulse 2s infinite;
            pointer-events: none;
        }

        .complaint-pulse {
            background: rgba(220, 53, 69, 0.3);
        }

        /* صناديق الإشعارات المنبثقة */
        .notification-box, .complaint-box {
            position: fixed;
            top: 70px;
            right: 20px;
            width: 380px;
            max-height: 500px;
            background: linear-gradient(135deg, #0d6efd 0%, #6f42c1 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(13, 110, 253, 0.2);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-20px) scale(0.95);
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .complaint-box {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            box-shadow: 0 10px 30px rgba(220, 53, 69, 0.2);
        }

        .notification-box.show, .complaint-box.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }

        /* رأس الصندوق */
        .notification-header, .complaint-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: between;
            align-items: center;
            color: white;
        }

        .notification-header h6, .complaint-header h6 {
            color: white;
            font-weight: 600;
            margin: 0;
            flex: 1;
        }

        .btn-close-notifications {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .btn-close-notifications:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        /* محتوى الإشعارات */
        .notification-content, .complaint-content {
            max-height: 350px;
            overflow-y: auto;
            padding: 10px;
        }

        /* عنصر الإشعار */
        .notification-item, .complaint-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .notification-item:hover, .complaint-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .notification-item.unread::before, .complaint-item.urgent::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #ff6b6b, #ee5a24);
        }

        .complaint-item.urgent::before {
            background: linear-gradient(to bottom, #ffa726, #ff7043);
        }

        /* أيقونة الإشعار */
        .notification-icon, .complaint-icon {
            font-size: 24px;
            margin-right: 15px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(45deg, #0d6efd, #6f42c1);
            color: white;
            flex-shrink: 0;
        }

        .complaint-icon {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
        }

        /* تفاصيل الإشعار */
        .notification-details, .complaint-details {
            flex: 1;
        }

        .notification-title, .complaint-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .notification-meta, .complaint-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 5px;
        }

        .order-number, .complaint-number {
            background: linear-gradient(45deg, #0d6efd, #6f42c1);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
        }

        .complaint-number {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
        }

        .status, .priority {
            background: #d1ecf1;
            color: #0c5460;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
        }

        .priority.high {
            background: #fff3cd;
            color: #856404;
        }

        .priority.critical {
            background: #f8d7da;
            color: #721c24;
        }

        .notification-time, .complaint-time {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* تذييل الصندوق */
        .notification-footer, .complaint-footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .view-all-link {
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .view-all-link:hover {
            color: #f8f9fa;
            text-decoration: underline;
        }

        /* الرسوم المتحركة */
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-3px);
            }
            60% {
                transform: translateY(-2px);
            }
        }

        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(1.4);
                opacity: 0;
            }
        }

        /* حالة عدم وجود إشعارات */
        .no-notifications {
            text-align: center;
            padding: 30px 20px;
            color: rgba(255, 255, 255, 0.8);
            font-style: italic;
        }

        /* تجاوب مع الشاشات الصغيرة */
        @media (max-width: 768px) {
            .notification-box, .complaint-box {
                width: 95%;
                right: 2.5%;
                left: 2.5%;
            }
        }
    </style>
</head>
<body id="main-body" data-user-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">
                <div class="d-flex align-items-center">
                    {% if company_info.header_logo %}
                        <img src="{{ company_info.header_logo.url }}?v={{ company_info.header_logo.url|length }}" alt="{{ company_info.name|default:'شعار النظام' }}" class="logo-img" id="header-logo">
                    {% elif company_info.logo %}
                        <img src="{{ company_info.logo.url }}?v={{ company_info.logo.url|length }}" alt="{{ company_info.name|default:'شعار النظام' }}" class="logo-img" id="header-logo">
                    {% else %}
                        <img src="{% static 'img/logo.png' %}" alt="شعار النظام" class="logo-img" id="header-logo">
                    {% endif %}
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}"><i class="fas fa-home"></i><span class="nav-text">الرئيسية</span></a>
                    </li>
                    {% if user.is_authenticated and user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin_dashboard' %}"><i class="fas fa-chart-line"></i><span class="nav-text">داش بورد الإدارة</span></a>
                    </li>
                    {% endif %}

                    <!-- الأقسام للمستخدمين المسجلين فقط -->
                    {% if user.is_authenticated %}
                        {% if user.is_staff %}
                            <!-- الموظفون يرون جميع الأقسام -->                            <li class="nav-item">
                                <a class="nav-link" href="/customers/">
                                    <i class="fas fa-users"></i><span class="nav-text">العملاء</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/orders/">
                                    <i class="fas fa-shopping-cart"></i><span class="nav-text">الطلبات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/inventory/">
                                    <i class="fas fa-boxes"></i><span class="nav-text">المخزون</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/inspections/">
                                    <i class="fas fa-clipboard-check"></i><span class="nav-text">المعاينات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/installations/">
                                    <i class="fas fa-tools"></i><span class="nav-text">التركيبات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/manufacturing/">
                                    <i class="fas fa-industry"></i><span class="nav-text">المصنع</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/complaints/">
                                    <i class="fas fa-exclamation-triangle"></i><span class="nav-text">الشكاوى</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/reports/list/">
                                    <i class="fas fa-chart-bar"></i><span class="nav-text">التقارير</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/database/">
                                    <i class="fas fa-database"></i><span class="nav-text">إدارة البيانات</span>
                                </a>
                            </li>
                        {% else %}                            <!-- المستخدمون العاديون يرون الأقسام المخصصة لهم فقط -->
                            {% for dept in user_parent_departments %}
                                <li class="nav-item">
                                    <a class="nav-link" href="/{{ dept.code }}/" title="{{ dept.name }}">
                                        <i class="{{ dept.icon|default:'fas fa-folder' }}"></i><span class="nav-text">{{ dept.name }}</span>
                                    </a>
                                </li>
                            {% endfor %}
                        {% endif %}
                    {% else %}                        <!-- رسالة للمستخدمين غير المسجلين -->
                        <li class="nav-item">
                            <span class="nav-link text-muted">
                                <i class="fas fa-lock"></i><span class="nav-text">يرجى تسجيل الدخول للوصول للأقسام</span>
                            </span>
                        </li>
                    {% endif %}
                </ul>
                <div class="d-flex align-items-center ms-auto">
                    {% if user.is_authenticated %}
                        <!-- صندوق الإشعارات الجميل 🎨 -->
                        <div class="notification-container me-3">
                            <!-- إشعارات الطلبات -->
                            <div class="notification-bell" id="orderNotificationBell">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="orderNotificationCount" style="display: none;">0</span>
                                <div class="notification-pulse"></div>
                            </div>

                            <!-- إشعارات الشكاوى -->
                            <div class="complaint-bell ms-2" id="complaintNotificationBell">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span class="complaint-badge" id="complaintNotificationCount" style="display: none;">0</span>
                                <div class="complaint-pulse"></div>
                            </div>
                        </div>
                        <button class="btn btn-outline-light dropdown-toggle" id="customUserBtn" type="button">
                            <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                        </button>
                    {% else %}
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-light"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- صناديق الإشعارات المنبثقة الجميلة 🎨 -->
    {% if user.is_authenticated %}

    <!-- صندوق إشعارات الطلبات -->
    <div id="orderNotificationBox" class="notification-box">
        <div class="notification-header">
            <h6 class="mb-0">
                <i class="fas fa-bell me-2"></i>
                إشعارات الطلبات
            </h6>
            <button class="btn-close-notifications" onclick="closeNotificationBox('order')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notification-content">
            <div class="no-notifications">جاري تحميل الإشعارات...</div>
        </div>
        <div class="notification-footer">
            <a href="{% url 'accounts:notifications_list' %}" class="view-all-link">عرض جميع الإشعارات</a>
        </div>
    </div>

    <!-- صندوق إشعارات الشكاوى -->
    <div id="complaintNotificationBox" class="complaint-box">
        <div class="complaint-header">
            <h6 class="mb-0">
                <i class="fas fa-exclamation-triangle me-2"></i>
                إشعارات الشكاوى
            </h6>
            <button class="btn-close-notifications" onclick="closeNotificationBox('complaint')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="complaint-content">
            <div class="no-notifications">جاري تحميل الشكاوى...</div>
        </div>
        <div class="complaint-footer">
            <a href="{% url 'accounts:complaints_list' %}" class="view-all-link">عرض جميع الشكاوى</a>
        </div>
    </div>

    {% endif %}

    <!-- Branch Messages - Only shown on home page -->
    {% if branch_messages and request.path == '/' %}
    <script>
        // Wait for all DOM content and resources to load for optimal display timing
        window.addEventListener('load', function() {
            const branchMessages = [
                {% for message in branch_messages %}
                    {
                        title: "{{ message.title|escapejs }}",
                        message: "{{ message.message|escapejs }}",
                        type: "{{ message.message_type|escapejs }}",
                        color: "{{ message.color|escapejs }}",
                        icon: "{{ message.icon|escapejs }}",
                        iconSize: "{{ message.icon_size|default:'medium'|escapejs }}",
                        displayStyle: "{{ message.display_style|default:'sweetalert2'|escapejs }}",
                        displayDuration: {{ message.display_duration|default:20 }},
                        autoClose: {{ message.auto_close|yesno:"true,false" }},
                        showCloseButton: {{ message.show_close_button|yesno:"true,false" }},
                        allowOutsideClick: {{ message.allow_outside_click|yesno:"true,false" }}
                    }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ];

            let currentMessageIndex = 0;
            const showMessage = () => {
                if (currentMessageIndex >= branchMessages.length) return;
                
                const message = branchMessages[currentMessageIndex];
                currentMessageIndex++;

                Swal.fire({
                    title: message.title,
                    html: message.message,
                    icon: getMessageIcon(message.type),
                    timer: message.displayDuration ? message.displayDuration * 1000 : 20000,
                    timerProgressBar: true,
                    showClass: {
                        popup: 'animate__animated animate__zoomIn animate__faster'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__zoomOut animate__faster'
                    },
                    customClass: {
                        popup: 'rtl-popup branch-message-popup',
                        title: 'rtl-title branch-message-title',
                        content: 'rtl-content branch-message-content',
                        container: 'branch-message-container'
                    },
                    showCloseButton: true,
                    position: 'center',
                    showConfirmButton: false,
                    allowOutsideClick: true,
                    width: '500px',
                    padding: '2rem',
                    background: getMessageBackground(message.color),
                    backdrop: `
                        rgba(0,0,0,0.6)
                        center
                        no-repeat
                    `,
                    didOpen: (toast) => {
                        const icon = document.createElement('i');
                        const iconSizeClass = getIconSizeClass(message.iconSize);
                        icon.className = `${message.icon} ${iconSizeClass}`;
                        const title = toast.querySelector('.swal2-title');
                        if (title) {
                            title.insertBefore(icon, title.firstChild);
                            icon.style.marginLeft = '10px';
                        }
                        
                        if (message.autoClose) {
                            toast.addEventListener('mouseenter', Swal.stopTimer);
                            toast.addEventListener('mouseleave', Swal.resumeTimer);
                        }
                    },
                    didClose: () => {
                        setTimeout(() => {
                            if (currentMessageIndex < branchMessages.length) {
                                showMessage();
                            }
                        }, 500);
                    }
                });
            };

            function getMessageIcon(type) {
                switch(type) {
                    case 'welcome':
                        return 'success';
                    case 'goal':
                        return 'info';
                    case 'announcement':
                        return 'info';
                    case 'holiday':
                        return 'warning';
                    default:
                        return 'info';
                }
            }

            function getIconSizeClass(size) {
                switch(size) {
                    case 'small': return 'fa-sm';
                    case 'medium': return 'fa-lg';
                    case 'large': return 'fa-2x';
                    default: return 'fa-lg';
                }
            }

            function getMessageBackground(color) {
                switch(color) {
                    case 'danger':
                        return '#f8d7da';
                    case 'warning':
                        return '#fff3cd';
                    case 'success':
                        return '#d4edda';
                    case 'info':
                        return '#d1ecf1';
                    default:
                        return '#ffffff';
                }
            }

            // Start showing messages after a short delay to ensure page is fully loaded
            setTimeout(showMessage, 500);
        });
    </script>

    <style>
        /* تحسين مظهر رسائل الفروع */
        .branch-message-popup {
            border-radius: 15px !important;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
            border: none !important;
            backdrop-filter: blur(10px);
        }

        .branch-message-title {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 600 !important;
            font-size: 1.4rem !important;
            color: #2c3e50 !important;
            text-align: center !important;
            margin-bottom: 15px !important;
        }

        .branch-message-content {
            font-family: 'Cairo', sans-serif !important;
            font-size: 1.1rem !important;
            line-height: 1.6 !important;
            color: #34495e !important;
            text-align: center !important;
        }

        .branch-message-container {
            padding: 20px !important;
            margin-bottom: 15px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08) !important;
            backdrop-filter: blur(10px) !important;
        }

        .branch-message-container i {
            margin-left: 10px !important;
            font-size: 1.4em !important;
            vertical-align: middle !important;
        }

        /* تحسين مظهر الخلفية المظللة */
        .swal2-container.swal2-backdrop-show {
            backdrop-filter: blur(8px) !important;
        }

        /* تحسين مظهر الأيقونات */
        .swal2-icon {
            margin: 20px auto !important;
            font-size: 3rem !important;
        }

        /* تحسين شريط التقدم */
        .swal2-timer-progress-bar {
            background: rgba(0,0,0,0.2) !important;
            height: 3px !important;
        }

        /* تحسين زر الإغلاق */
        .swal2-close {
            font-size: 1.8rem !important;
            color: rgba(0,0,0,0.5) !important;
            transition: all 0.3s ease !important;
        }

        .swal2-close:hover {
            color: rgba(0,0,0,0.8) !important;
            transform: scale(1.1) !important;
        }

        @media (max-width: 768px) {
            .branch-message-popup {
                width: 90% !important;
                margin: 10px auto !important;
            }
            
            .branch-message-container {
                margin: 5px !important;
                padding: 15px !important;
            }

            .branch-message-title {
                font-size: 1.2rem !important;
            }

            .branch-message-content {
                font-size: 1rem !important;
            }
        }
    </style>
    {% endif %}


    <!-- قائمة المستخدم المنبثقة خارج تدفق الهيدر -->
    <div id="customUserDropdown" class="custom-dropdown-menu" style="display:none; position:fixed; top:70px; left:90px; z-index:2147483647; min-width:260px; max-width:95vw; background:#fff; border-radius:12px; box-shadow:0 8px 32px rgba(0,0,0,0.18); border:1px solid #eee;">
        <div class="dropdown-header fw-bold py-2 px-3 border-bottom">القائمة الشخصية</div>
        <div class="px-3 py-2">
            <label for="themeSelector" class="form-label" style="font-weight: bold;">اختر الثيم المناسب</label>
            <select id="themeSelector" class="form-select">
                <option value="default">الثيم الإفتراضي</option>
                <option value="custom-theme">نسخة الثيم الإفتراضي القابلة للتعديل</option>
                <option value="modern-black">الثيم الأسود العصري</option>
            </select>
        </div>
        <hr class="my-1">
        <a class="dropdown-item" href="{% url 'accounts:profile' %}"><i class="fas fa-id-card"></i> الملف الشخصي</a>
        {% if user.is_staff %}
        <a class="dropdown-item" href="{% url 'admin:index' %}"><i class="fas fa-cog"></i> لوحة الإدارة</a>
        {% endif %}
        <hr class="my-1">
        <a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
    </div>

    <script>
    // 🎨 نظام الإشعارات الإبداعي والجميل
    document.addEventListener('DOMContentLoaded', function() {
        // عناصر الإشعارات
        const orderBell = document.getElementById('orderNotificationBell');
        const complaintBell = document.getElementById('complaintNotificationBell');
        const orderBox = document.getElementById('orderNotificationBox');
        const complaintBox = document.getElementById('complaintNotificationBox');

        // إظهار/إخفاء صندوق إشعارات الطلبات
        if (orderBell && orderBox) {
            orderBell.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleNotificationBox('order');
            });
        }

        // إظهار/إخفاء صندوق إشعارات الشكاوى
        if (complaintBell && complaintBox) {
            complaintBell.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleNotificationBox('complaint');
            });
        }

        // إغلاق الصناديق عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.notification-box') && !e.target.closest('.notification-bell')) {
                closeNotificationBox('order');
            }
            if (!e.target.closest('.complaint-box') && !e.target.closest('.complaint-bell')) {
                closeNotificationBox('complaint');
            }
        });

        // تأثير النقر على الإشعارات
        document.querySelectorAll('.notification-item, .complaint-item').forEach(item => {
            item.addEventListener('click', function() {
                // تأثير النقر
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // تحديد كمقروء
                this.classList.remove('unread', 'urgent');

                // الحصول على معرف الإشعار
                const notificationId = this.getAttribute('data-notification-id');
                const isComplaint = this.classList.contains('complaint-item');

                // تحديد الإشعار كمقروء في الخادم
                if (notificationId) {
                    markNotificationAsRead(notificationId, isComplaint ? 'complaint' : 'order');
                }

                // توجيه المستخدم للمكان المناسب
                redirectToNotificationTarget(this);

                // تحديث العداد
                updateNotificationCount();
            });
        });

        // تحميل الإشعارات فور تحميل الصفحة
        loadNotificationsFromServer();

        // تحديث العدادات كل 30 ثانية
        setInterval(function() {
            loadNotificationsFromServer();
        }, 30000);
    });

    // 🎨 الدوال المساعدة للإشعارات الجميلة
    function toggleNotificationBox(type) {
        const orderBox = document.getElementById('orderNotificationBox');
        const complaintBox = document.getElementById('complaintNotificationBox');

        if (type === 'order') {
            // إغلاق صندوق الشكاوى إذا كان مفتوحاً
            if (complaintBox) {
                complaintBox.classList.remove('show');
            }

            // تبديل صندوق الطلبات
            if (orderBox) {
                orderBox.classList.toggle('show');

                // تأثير صوتي لطيف (اختياري)
                if (orderBox.classList.contains('show')) {
                    playNotificationSound();
                }
            }
        } else if (type === 'complaint') {
            // إغلاق صندوق الطلبات إذا كان مفتوحاً
            if (orderBox) {
                orderBox.classList.remove('show');
            }

            // تبديل صندوق الشكاوى
            if (complaintBox) {
                complaintBox.classList.toggle('show');

                // تأثير صوتي لطيف (اختياري)
                if (complaintBox.classList.contains('show')) {
                    playNotificationSound();
                }
            }
        }
    }

    function closeNotificationBox(type) {
        const orderBox = document.getElementById('orderNotificationBox');
        const complaintBox = document.getElementById('complaintNotificationBox');

        if (type === 'order' && orderBox) {
            orderBox.classList.remove('show');
        } else if (type === 'complaint' && complaintBox) {
            complaintBox.classList.remove('show');
        }
    }

    function updateNotificationCount() {
        // تحديث عداد إشعارات الطلبات
        const unreadOrders = document.querySelectorAll('.notification-item.unread').length;
        const orderCountBadge = document.getElementById('orderNotificationCount');
        if (orderCountBadge) {
            if (unreadOrders > 0) {
                orderCountBadge.textContent = unreadOrders;
                orderCountBadge.style.display = 'flex';
            } else {
                orderCountBadge.style.display = 'none';
            }
        }

        // تحديث عداد إشعارات الشكاوى
        const urgentComplaints = document.querySelectorAll('.complaint-item.urgent').length;
        const complaintCountBadge = document.getElementById('complaintNotificationCount');
        if (complaintCountBadge) {
            if (urgentComplaints > 0) {
                complaintCountBadge.textContent = urgentComplaints;
                complaintCountBadge.style.display = 'flex';
            } else {
                complaintCountBadge.style.display = 'none';
            }
        }
    }

    function playNotificationSound() {
        // تأثير صوتي لطيف وخفيف
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (e) {
            // تجاهل الأخطاء الصوتية
        }
    }

    function markNotificationAsRead(notificationId, type) {
        // إرسال طلب AJAX لتحديد الإشعار كمقروء
        fetch(`/accounts/notifications/${type}/${notificationId}/mark-read/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الواجهة
                const item = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (item) {
                    item.classList.remove('unread', 'urgent');
                }
                updateNotificationCount();
            }
        })
        .catch(error => {
            console.log('تم تحديد الإشعار محلياً');
        });
    }

    function loadNotificationsFromServer() {
        // تحميل الإشعارات الحقيقية من الخادم
        fetch('/accounts/notifications/data/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث إشعارات الطلبات
                updateOrderNotifications(data.order_notifications);

                // تحديث إشعارات الشكاوى
                updateComplaintNotifications(data.complaint_notifications);

                // تحديث العدادات
                updateNotificationBadges(data.unread_orders_count, data.urgent_complaints_count);
            } else {
                // عرض رسالة خطأ
                const orderContainer = document.querySelector('#orderNotificationBox .notification-content');
                const complaintContainer = document.querySelector('#complaintNotificationBox .complaint-content');

                if (orderContainer) {
                    orderContainer.innerHTML = '<div class="no-notifications">❌ خطأ في تحميل الإشعارات</div>';
                }
                if (complaintContainer) {
                    complaintContainer.innerHTML = '<div class="no-notifications">❌ خطأ في تحميل الشكاوى</div>';
                }
            }
        })
        .catch(error => {
            console.log('خطأ في تحميل الإشعارات:', error);
            // عرض رسالة خطأ
            const orderContainer = document.querySelector('#orderNotificationBox .notification-content');
            const complaintContainer = document.querySelector('#complaintNotificationBox .complaint-content');

            if (orderContainer) {
                orderContainer.innerHTML = '<div class="no-notifications">📭 لا توجد إشعارات جديدة</div>';
            }
            if (complaintContainer) {
                complaintContainer.innerHTML = '<div class="no-notifications">📢 لا توجد شكاوى جديدة</div>';
            }
        });
    }

    function updateOrderNotifications(notifications) {
        const container = document.querySelector('#orderNotificationBox .notification-content');
        if (!container) return;

        if (notifications.length === 0) {
            container.innerHTML = '<div class="no-notifications">📭 لا توجد إشعارات جديدة</div>';
            return;
        }

        container.innerHTML = notifications.map(notification => `
            <div class="notification-item ${notification.is_read ? '' : 'unread'}" data-notification-id="${notification.id}" style="cursor: pointer;">
                <div class="notification-icon ${notification.notification_type}">
                    ${notification.icon}
                </div>
                <div class="notification-details">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-meta">
                        <span class="order-number">${notification.order_number}</span>
                        <span class="status">${notification.status}</span>
                    </div>
                    <div class="notification-time">منذ ${notification.time_ago}</div>
                </div>
            </div>
        `).join('');

        // إضافة event listeners للإشعارات الجديدة
        container.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', function() {
                // تأثير النقر
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // تحديد كمقروء
                this.classList.remove('unread');

                // الحصول على معرف الإشعار
                const notificationId = this.getAttribute('data-notification-id');

                // تحديد الإشعار كمقروء في الخادم
                if (notificationId) {
                    markNotificationAsRead(notificationId, 'order');
                }

                // توجيه المستخدم للمكان المناسب
                redirectToNotificationTarget(this);

                // تحديث العداد
                updateNotificationCount();
            });
        });
    }

    function updateComplaintNotifications(notifications) {
        const container = document.querySelector('#complaintNotificationBox .complaint-content');
        if (!container) return;

        if (notifications.length === 0) {
            container.innerHTML = '<div class="no-notifications">📢 لا توجد شكاوى جديدة</div>';
            return;
        }

        container.innerHTML = notifications.map(notification => `
            <div class="complaint-item ${notification.is_read ? '' : 'urgent'}" data-notification-id="${notification.id}" style="cursor: pointer;">
                <div class="complaint-icon ${notification.complaint_type}">
                    ${notification.icon}
                </div>
                <div class="complaint-details">
                    <div class="complaint-title">${notification.title}</div>
                    <div class="complaint-meta">
                        <span class="complaint-number">${notification.complaint_number}</span>
                        <span class="priority ${notification.priority}">${notification.priority}</span>
                    </div>
                    <div class="complaint-time">منذ ${notification.time_ago}</div>
                </div>
            </div>
        `).join('');

        // إضافة event listeners للشكاوى الجديدة
        container.querySelectorAll('.complaint-item').forEach(item => {
            item.addEventListener('click', function() {
                // تأثير النقر
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // تحديد كمقروء
                this.classList.remove('urgent');

                // الحصول على معرف الإشعار
                const notificationId = this.getAttribute('data-notification-id');

                // تحديد الإشعار كمقروء في الخادم
                if (notificationId) {
                    markNotificationAsRead(notificationId, 'complaint');
                }

                // توجيه المستخدم للمكان المناسب
                redirectToNotificationTarget(this);

                // تحديث العداد
                updateNotificationCount();
            });
        });
    }

    function updateNotificationBadges(orderCount, complaintCount) {
        const orderBadge = document.getElementById('orderNotificationCount');
        const complaintBadge = document.getElementById('complaintNotificationCount');

        if (orderBadge) {
            if (orderCount > 0) {
                orderBadge.textContent = orderCount;
                orderBadge.style.display = 'flex';
            } else {
                orderBadge.style.display = 'none';
            }
        }

        if (complaintBadge) {
            if (complaintCount > 0) {
                complaintBadge.textContent = complaintCount;
                complaintBadge.style.display = 'flex';
            } else {
                complaintBadge.style.display = 'none';
            }
        }
    }

    function redirectToNotificationTarget(notificationElement) {
        // توجيه المستخدم للمكان المناسب حسب نوع الإشعار
        const orderNumber = notificationElement.querySelector('.order-number, .complaint-number');

        if (notificationElement.classList.contains('complaint-item')) {
            // توجيه لصفحة الشكاوى
            window.location.href = '/complaints/';
        } else if (notificationElement.classList.contains('notification-item')) {
            // توجيه لصفحة الطلبات
            const notificationType = notificationElement.querySelector('.notification-icon').className;

            if (notificationType.includes('inspection')) {
                // توجيه لصفحة المعاينات
                window.location.href = '/inspections/';
            } else if (notificationType.includes('manufacturing')) {
                // توجيه لصفحة التصنيع
                window.location.href = '/manufacturing/';
            } else if (notificationType.includes('installation')) {
                // توجيه لصفحة التركيبات
                window.location.href = '/installations/';
            } else {
                // توجيه لصفحة الطلبات العامة
                window.location.href = '/orders/';
            }
        }
    }

    // تحديث العدادات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateNotificationCount();
        loadNotificationsFromServer();

        var userBtn = document.getElementById('customUserBtn');
        var userDropdown = document.getElementById('customUserDropdown');
        if (userBtn && userDropdown) {
            userBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                userDropdown.style.display = userDropdown.style.display === 'block' ? 'none' : 'block';
                userDropdown.focus();
            });
        }
        document.addEventListener('click', function(e) {
            // إغلاق القوائم إذا تم الضغط خارجها
            if (userDropdown) userDropdown.style.display = 'none';
        });
        if (userDropdown) userDropdown.addEventListener('click', function(e) { e.stopPropagation(); });
    });
    </script>

    <!-- Main Content -->
    <main class="container-fluid mt-4 flex-grow" id="main-content">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer المُحسَّن - تصميم أفقي -->
    <footer class="footer-area text-white py-2" style="background-color: var(--accent);" id="main-footer">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                <!-- معلومات الشركة والتواصل الاجتماعي -->
                <div class="d-flex align-items-center">
                    <h6 class="footer-title mb-0 me-3">
                        <i class="fas fa-building me-2"></i>
                        {{ company_info.name }}
                    </h6>
                    {% if company_info.facebook or company_info.twitter or company_info.instagram or company_info.linkedin %}
                    <div class="social-icons d-flex">
                        {% if company_info.facebook %}
                        <a href="{{ company_info.facebook }}" class="social-icon me-2" target="_blank" title="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        {% endif %}
                        {% if company_info.twitter %}
                        <a href="{{ company_info.twitter }}" class="social-icon me-2" target="_blank" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        {% endif %}
                        {% if company_info.instagram %}
                        <a href="{{ company_info.instagram }}" class="social-icon me-2" target="_blank" title="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        {% endif %}
                        {% if company_info.linkedin %}
                        <a href="{{ company_info.linkedin }}" class="social-icon me-2" target="_blank" title="LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                <!-- الروابط السريعة -->
                <div class="d-flex align-items-center">
                    <a href="{% url 'home' %}" class="footer-link mx-2">
                        <i class="fas fa-home"></i>
                    </a>
                    <a href="{% url 'about' %}" class="footer-link mx-2">
                        <i class="fas fa-info-circle"></i>
                    </a>
                    <a href="{% url 'contact' %}" class="footer-link mx-2">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>

                <!-- معلومات الاتصال -->
                <div class="d-flex align-items-center gap-3">
                    {% if company_info.phone %}
                    <a href="tel:{{ company_info.phone }}" class="footer-contact-item" title="رقم الهاتف">
                        <i class="fas fa-phone-alt"></i>
                        <span class="d-none d-md-inline small">{{ company_info.phone }}</span>
                    </a>
                    {% endif %}
                    {% if company_info.email %}
                    <a href="mailto:{{ company_info.email }}" class="footer-contact-item" title="البريد الإلكتروني">
                        <i class="fas fa-at"></i>
                        <span class="d-none d-md-inline small">{{ company_info.email }}</span>
                    </a>
                    {% endif %}
                    {% if company_info.address %}
                    <div class="footer-contact-item" title="العنوان">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="d-none d-md-inline small">{{ company_info.address }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- تفاصيل الشركة (وصف مختصر) -->
            {% if company_info.description %}
            <div class="text-center mt-2">
                <span class="small">{{ company_info.description }}</span>
            </div>
            {% endif %}

            <!-- حقوق النشر -->
            <div class="text-center mt-2">
                <p class="copyright-text mb-0 small">
                    <i class="far fa-copyright me-1"></i>
                    {{ current_year }}
                    {{ company_info.copyright_text|default:"جميع الحقوق محفوظة" }}
                </p>
            </div>
        </div>
    </footer>


            <style>
    /* أنماط الفوتر الأساسية */
    .footer-title {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .social-icons {
        gap: 5px;
    }

    .social-icon {
            width: 22px;
            height: 22px;
            font-size: 0.7rem;
        }

        .copyright-text {
            font-size: 0.7rem;
        }
    }
    </style>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/smooth-navigation.js' %}"></script>
    

    <script src="{% static 'js/custom-theme-animations.js' %}"></script>

    <!-- Database Connection Test JS -->
    {% if 'database' in request.path %}
    <script src="{% static 'js/db_connection_test.js' %}"></script>
    {% endif %}

    <!-- Restore Success Alert -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // البحث عن رسائل النجاح المتعلقة بالاستعادة
            const messages = document.querySelectorAll('.alert-success');

            messages.forEach(function(messageElement) {
                const messageText = messageElement.textContent.trim();

                // التحقق من أن الرسالة تتعلق بالاستعادة
                if (messageText.includes('تم استعادة النسخة الاحتياطية بنجاح') ||
                    messageText.includes('تمت الاستعادة بنجاح') ||
                    messageText.includes('تم استعادة البيانات بنجاح') ||
                    messageText.includes('تمت استعادة البيانات بنجاح')) {

                    // إخفاء الرسالة العادية
                    messageElement.style.display = 'none';

                    // عرض SweetAlert مع التعليمات
                    Swal.fire({
                        title: 'تمت الاستعادة بنجاح! 🎉',
                        html: `
                            <div style="text-align: right; direction: rtl; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                                <p style="font-size: 16px; margin-bottom: 20px; color: #2c3e50;">
                                    <strong>لضمان ظهور جميع البيانات، يرجى اتباع إحدى الخطوات التالية:</strong>
                                </p>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                                    <p style="margin: 8px 0; color: #495057;">
                                        <i class="fas fa-sync-alt" style="color: #007bff; margin-left: 8px;"></i>
                                        <strong>1. تحديث الصفحة (F5)</strong>
                                    </p>
                                    <p style="margin: 8px 0; color: #495057;">
                                        <i class="fas fa-sign-in-alt" style="color: #28a745; margin-left: 8px;"></i>
                                        <strong>2. إعادة تسجيل الدخول</strong>
                                    </p>
                                    <p style="margin: 8px 0; color: #495057;">
                                        <i class="fas fa-clock" style="color: #ffc107; margin-left: 8px;"></i>
                                        <strong>3. انتظار دقيقة واحدة للتحديث التلقائي</strong>
                                    </p>
                                </div>
                                <p style="font-size: 14px; color: #6c757d; margin-top: 15px;">
                                    <i class="fas fa-info-circle" style="margin-left: 5px;"></i>
                                    هذا أمر طبيعي ويحدث بسبب التخزين المؤقت للبيانات
                                </p>
                            </div>
                        `,
                        icon: 'success',
                        showCancelButton: true,
                        confirmButtonText: '<i class="fas fa-sync-alt"></i> تحديث الصفحة الآن',
                        cancelButtonText: '<i class="fas fa-times"></i> إغلاق',
                        confirmButtonColor: '#007bff',
                        cancelButtonColor: '#6c757d',
                        width: '600px',
                        customClass: {
                            popup: 'rtl-popup',
                            title: 'rtl-title',
                            content: 'rtl-content'
                        },
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // تحديث الصفحة فوراً
                            location.reload();
                        } else {
                            // إضافة رسالة تذكير للمستخدم
                            const reminderToast = Swal.mixin({
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 5000,
                                timerProgressBar: true,
                                didOpen: (toast) => {
                                    toast.addEventListener('mouseenter', Swal.stopTimer)
                                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                                }
                            });

                            reminderToast.fire({
                                icon: 'info',
                                title: 'تذكير: قم بتحديث الصفحة لرؤية جميع البيانات'
                            });

                            // تحديث تلقائي بعد دقيقة واحدة
                            setTimeout(() => {
                                Swal.fire({
                                    title: 'تحديث تلقائي',
                                    text: 'سيتم تحديث الصفحة الآن لإظهار جميع البيانات',
                                    icon: 'info',
                                    timer: 3000,
                                    timerProgressBar: true,
                                    showConfirmButton: false
                                }).then(() => {
                                    location.reload();
                                });
                            }, 60000); // 60 ثانية
                        }
                    });
                }
            });
        });
    </script>

    <!-- CSS للـ RTL SweetAlert -->
    <style>
        .rtl-popup {
            direction: rtl !important;
            text-align: right !important;
        }

        .rtl-title {
            direction: rtl !important;
            text-align: center !important;
        }

        .rtl-content {
            direction: rtl !important;
            text-align: right !important;
        }

        .swal2-html-container {
            direction: rtl !important;
            text-align: right !important;
        }

        .swal2-actions {
            direction: ltr !important;
        }

        .swal2-confirm {
            margin-left: 10px !important;
        }
    </style>

    <!-- JavaScript لتحديث اللوغو مباشرة عند رفعه -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من أننا في صفحة إعدادات الشركة
            if (window.location.pathname.includes('/admin/accounts/companyinfo/')) {
                // البحث عن حقول رفع اللوغوهات
                const logoInput = document.querySelector('input[type="file"][name="logo"]');
                const headerLogoInput = document.querySelector('input[type="file"][name="header_logo"]');
                const headerLogo = document.getElementById('header-logo');
                const homeLogo = document.getElementById('home-logo');
                const aboutLogo = document.getElementById('about-logo');
                
                // معالجة لوغو النظام العام
                if (logoInput) {
                    logoInput.addEventListener('change', function(e) {
                        const file = e.target.files[0];
                        if (file) {
                            const tempUrl = URL.createObjectURL(file);
                            
                            // تحديث اللوغوهات في الصفحات الأخرى (غير الهيدر)
                            const otherLogos = [homeLogo, aboutLogo];
                            otherLogos.forEach(logo => {
                                if (logo) {
                                    logo.src = tempUrl + '?v=' + Date.now();
                                    logo.style.opacity = '0.7';
                                    setTimeout(() => {
                                        logo.style.opacity = '1';
                                    }, 200);
                                }
                            });
                            
                            // عرض رسالة تأكيد
                            Swal.fire({
                                title: 'تم رفع لوغو النظام بنجاح! 🎉',
                                text: 'سيتم تحديث اللوغو في جميع صفحات النظام',
                                icon: 'success',
                                timer: 2000,
                                timerProgressBar: true,
                                showConfirmButton: false,
                                position: 'top-end',
                                toast: true
                            });
                        }
                    });
                }
                
                // معالجة لوغو الهيدر
                if (headerLogoInput) {
                    headerLogoInput.addEventListener('change', function(e) {
                        const file = e.target.files[0];
                        if (file) {
                            const tempUrl = URL.createObjectURL(file);
                            
                            // تحديث لوغو الهيدر فقط
                            if (headerLogo) {
                                headerLogo.src = tempUrl + '?v=' + Date.now();
                                headerLogo.style.opacity = '0.7';
                                setTimeout(() => {
                                    headerLogo.style.opacity = '1';
                                }, 200);
                            }
                            
                            // عرض رسالة تأكيد
                            Swal.fire({
                                title: 'تم رفع لوغو الهيدر بنجاح! 🎉',
                                text: 'سيتم تحديث لوغو الهيدر في جميع صفحات النظام',
                                icon: 'success',
                                timer: 2000,
                                timerProgressBar: true,
                                showConfirmButton: false,
                                position: 'top-end',
                                toast: true
                            });
                        }
                    });
                }
            }
            
            // تحديث جميع اللوغوهات عند تحميل الصفحة لتجنب التخزين المؤقت
            const allLogos = ['header-logo', 'home-logo', 'about-logo'];
            allLogos.forEach(logoId => {
                const logo = document.getElementById(logoId);
                if (logo && logo.src) {
                    // إضافة timestamp للصورة لتجنب التخزين المؤقت
                    if (!logo.src.includes('?v=')) {
                        logo.src = logo.src + '?v=' + Date.now();
                    }
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
