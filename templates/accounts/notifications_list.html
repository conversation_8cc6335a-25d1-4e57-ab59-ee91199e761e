{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الإشعارات{% endblock %}

{% block extra_css %}
<style>
    .notifications-page {
        padding: 20px 0;
    }
    
    .notification-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
        padding: 20px;
        transition: all 0.3s ease;
        border-left: 4px solid #0d6efd;
    }
    
    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    
    .notification-card.unread {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-left-color: #0d6efd;
    }
    
    .notification-card.read {
        opacity: 0.8;
        border-left-color: #6c757d;
    }
    
    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .notification-icon {
        font-size: 24px;
        margin-right: 15px;
    }
    
    .notification-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    
    .notification-meta {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .meta-badge {
        background: #0d6efd;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .notification-time {
        color: #6c757d;
        font-size: 12px;
    }
    
    .priority-high {
        border-left-color: #ffc107 !important;
    }
    
    .priority-urgent {
        border-left-color: #dc3545 !important;
    }
    
    .no-notifications {
        text-align: center;
        padding: 50px;
        color: #6c757d;
    }
    
    .filter-tabs {
        margin-bottom: 20px;
    }
    
    .filter-tabs .nav-link {
        color: #6c757d;
        border: none;
        border-bottom: 2px solid transparent;
    }
    
    .filter-tabs .nav-link.active {
        color: #0d6efd;
        border-bottom-color: #0d6efd;
        background: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid notifications-page">
    {% csrf_token %}
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>🔔 قائمة الإشعارات</h2>
                <div class="d-flex gap-2">
                    <span class="badge bg-primary">{{ unread_count }} غير مقروء</span>
                    <span class="badge bg-secondary">{{ total_count }} إجمالي</span>
                </div>
            </div>
            
            <!-- تبويبات الفلترة -->
            <ul class="nav nav-tabs filter-tabs" id="notificationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                        جميع الإشعارات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="unread-tab" data-bs-toggle="tab" data-bs-target="#unread" type="button" role="tab">
                        غير مقروءة ({{ unread_count }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="read-tab" data-bs-toggle="tab" data-bs-target="#read" type="button" role="tab">
                        مقروءة
                    </button>
                </li>
            </ul>
            
            <!-- محتوى التبويبات -->
            <div class="tab-content" id="notificationTabsContent">
                <div class="tab-pane fade show active" id="all" role="tabpanel">
                    {% if notifications %}
                        {% for notification in notifications %}
                        <div class="notification-card {% if not notification.is_read %}unread{% else %}read{% endif %} priority-{{ notification.priority }}" 
                             data-notification-id="{{ notification.id }}">
                            <div class="notification-header">
                                <div class="d-flex align-items-center">
                                    <span class="notification-icon">{{ notification.get_icon }}</span>
                                    <div>
                                        <div class="notification-title">{{ notification.title }}</div>
                                        <div class="notification-meta">
                                            <span class="meta-badge">{{ notification.order_number }}</span>
                                            <span class="meta-badge" style="background: #28a745;">{{ notification.status }}</span>
                                            <span class="meta-badge" style="background: #6c757d;">{{ notification.get_priority_display }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="notification-time">{{ notification.get_time_ago }}</div>
                                    {% if not notification.is_read %}
                                        <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                                data-notification-id="{{ notification.id }}">
                                            تحديد كمقروء
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-notifications">
                            📭 لا توجد إشعارات
                        </div>
                    {% endif %}
                </div>
                
                <div class="tab-pane fade" id="unread" role="tabpanel">
                    {% for notification in notifications %}
                        {% if not notification.is_read %}
                        <div class="notification-card unread priority-{{ notification.priority }}" 
                             data-notification-id="{{ notification.id }}">
                            <div class="notification-header">
                                <div class="d-flex align-items-center">
                                    <span class="notification-icon">{{ notification.get_icon }}</span>
                                    <div>
                                        <div class="notification-title">{{ notification.title }}</div>
                                        <div class="notification-meta">
                                            <span class="meta-badge">{{ notification.order_number }}</span>
                                            <span class="meta-badge" style="background: #28a745;">{{ notification.status }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="notification-time">{{ notification.get_time_ago }}</div>
                                    <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                            data-notification-id="{{ notification.id }}">
                                        تحديد كمقروء
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    {% empty %}
                        <div class="no-notifications">
                            ✅ جميع الإشعارات مقروءة
                        </div>
                    {% endfor %}
                </div>
                
                <div class="tab-pane fade" id="read" role="tabpanel">
                    {% for notification in notifications %}
                        {% if notification.is_read %}
                        <div class="notification-card read">
                            <div class="notification-header">
                                <div class="d-flex align-items-center">
                                    <span class="notification-icon">{{ notification.get_icon }}</span>
                                    <div>
                                        <div class="notification-title">{{ notification.title }}</div>
                                        <div class="notification-meta">
                                            <span class="meta-badge">{{ notification.order_number }}</span>
                                            <span class="meta-badge" style="background: #28a745;">{{ notification.status }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="notification-time">{{ notification.get_time_ago }}</div>
                                    <span class="badge bg-success">✅ مقروء</span>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    {% empty %}
                        <div class="no-notifications">
                            📭 لا توجد إشعارات مقروءة
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديد الإشعار كمقروء
    document.querySelectorAll('.mark-read-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            
            // الحصول على CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            const headers = {
                'Content-Type': 'application/json',
            };

            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken.value;
            }

            fetch(`/accounts/notifications/order/${notificationId}/mark-read/`, {
                method: 'POST',
                headers: headers,
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        });
    });
});
</script>
{% endblock %}
