{% extends 'base.html' %}
{% load static %}
{% load unified_status_tags %}

{% block title %}قائمة التركيبات{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-list text-primary"></i>
                قائمة التركيبات
            </h1>
            <!-- عرض الفلتر المطبق إن وجد -->
            {% if request.GET.status %}
                <div class="mt-2">
                    <span class="badge badge-primary fs-6">
                        <i class="fas fa-filter me-1"></i>
                        مفلتر حسب: 
                        {% if request.GET.status == 'completed' %}مكتمل
                        {% elif request.GET.status == 'scheduled' %}مجدول
                        {% elif request.GET.status == 'needs_scheduling' %}بانتظار الجدولة
                        {% elif request.GET.status == 'under_manufacturing' %}تحت التصنيع
                        {% elif request.GET.status == 'in_installation' %}قيد التركيب
                        {% elif request.GET.status == 'modification_required' %}يحتاج تعديل
                        {% elif request.GET.status == 'cancelled' %}ملغي
                        {% elif request.GET.status == 'modification_in_progress' %}التعديل قيد التنفيذ
                        {% elif request.GET.status == 'modification_completed' %}التعديل مكتمل
                        {% else %}{{ request.GET.status }}{% endif %}
                    </span>
                </div>
            {% elif request.GET.search %}
                <div class="mt-2">
                    <span class="badge badge-info fs-6">
                        <i class="fas fa-search me-1"></i>
                        بحث عن: "{{ request.GET.search }}"
                    </span>
                </div>
            {% endif %}
        </div>
        <a href="{% url 'installations:dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
        </a>
    </div>

    <!-- فلاتر البحث المحسّنة -->
    <div class="card filter-card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i>
                فلاتر البحث والعرض
            </h6>
            <div class="filter-stats">
                <span class="badge badge-info">{{ total_count }} نتيجة</span>
                {% if needs_scheduling_count %}
                    <span class="badge badge-warning">{{ needs_scheduling_count }} بحاجة جدولة</span>
                {% endif %}
                {% if scheduled_count %}
                    <span class="badge badge-primary">{{ scheduled_count }} مجدول</span>
                {% endif %}
                {% if completed_count %}
                    <span class="badge badge-success">{{ completed_count }} مكتمل</span>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3" id="filterForm">
                <!-- الصف الأول - الفلاتر الأساسية -->
                <div class="col-md-2">
                    {{ filter_form.status.label_tag }}
                    {{ filter_form.status }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.team.label_tag }}
                    {{ filter_form.team }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.location_type.label_tag }}
                    {{ filter_form.location_type }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.order_status.label_tag }}
                    {{ filter_form.order_status }}
                </div>
                <div class="col-md-2">
                    <label for="page_size" class="form-label">عدد الصفوف:</label>
                    <select name="page_size" id="page_size" class="form-select" onchange="this.form.submit()">
                        <option value="10" {% if current_filters.page_size == 10 %}selected{% endif %}>10</option>
                        <option value="25" {% if current_filters.page_size == 25 or not current_filters.page_size %}selected{% endif %}>25</option>
                        <option value="50" {% if current_filters.page_size == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if current_filters.page_size == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="fas fa-search"></i> تطبيق
                        </button>
                        <a href="{% url 'installations:installation_list' %}" class="btn btn-outline-secondary" title="مسح الفلاتر">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
                
                <!-- الصف الثاني - التواريخ والبحث -->
                <div class="col-md-3">
                    {{ filter_form.date_from.label_tag }}
                    {{ filter_form.date_from }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.date_to.label_tag }}
                    {{ filter_form.date_to }}
                </div>
                <div class="col-md-6">
                    {{ filter_form.search.label_tag }}
                    <div class="input-group">
                        {{ filter_form.search }}
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- عرض الفلاتر المطبقة -->
            {% if has_filters %}
            <div class="mt-3 pt-3 border-top">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="applied-filters">
                        <span class="text-muted me-2">الفلاتر المطبقة:</span>
                        {% if current_filters.status %}
                            <span class="badge badge-primary me-1">
                                الحالة: {% if current_filters.status == 'needs_scheduling' %}بحاجة جدولة{% elif current_filters.status == 'scheduled' %}مجدول{% elif current_filters.status == 'completed' %}مكتمل{% else %}{{ current_filters.status }}{% endif %}
                                <a href="?{% for key, value in current_filters.items %}{% if key != 'status' and value %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white ms-1">×</a>
                            </span>
                        {% endif %}
                        {% if current_filters.search %}
                            <span class="badge badge-info me-1">
                                بحث: "{{ current_filters.search }}"
                                <a href="?{% for key, value in current_filters.items %}{% if key != 'search' and value %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white ms-1">×</a>
                            </span>
                        {% endif %}
                        {% if current_filters.team %}
                            <span class="badge badge-success me-1">
                                فريق محدد
                                <a href="?{% for key, value in current_filters.items %}{% if key != 'team' and value %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white ms-1">×</a>
                            </span>
                        {% endif %}
                        {% if current_filters.date_from or current_filters.date_to %}
                            <span class="badge badge-warning me-1">
                                فترة زمنية محددة
                                <a href="?{% for key, value in current_filters.items %}{% if key != 'date_from' and key != 'date_to' and value %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white ms-1">×</a>
                            </span>
                        {% endif %}
                    </div>
                    <a href="{% url 'installations:installation_list' %}" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-times me-1"></i> مسح جميع الفلاتر
                    </a>
                </div>
            </div>
            {% endif %}
            
            <!-- رسالة توضيحية للطلبات التي تحتاج جدولة -->
            {% if current_filters.status == 'needs_scheduling' or not has_filters %}
            <div class="alert alert-info mt-3" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> تظهر هنا الطلبات الجاهزة للتركيب والتي تحتاج إلى جدولة. يمكنك النقر على زر الجدولة لإنشاء موعد تركيب.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- جدول التركيبات -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <h6 class="m-0 font-weight-bold text-white">
                <i class="fas fa-table"></i>
                التركيبات ({{ page_obj.paginator.count }})
            </h6>
        </div>
        <div class="card-body">
            {% if installations %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover installations-table" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>المكان</th>
                                <th>البائع</th>
                                <th>رقم العقد</th>
                                <th>رقم الفاتورة</th>
                                <th>الفرع</th>
                                <th>الفريق</th>
                                <th>التاريخ</th>
                                <th>الموعد</th>
                                <th>القيمة المتبقية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in installations %}
                            <tr>
                                <td>
                                    <a href="{% url 'orders:order_detail' item.order.id %}" class="font-weight-bold text-primary" title="تفاصيل الطلب الأساسي">
                                        {{ item.order.order_number }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{% url 'customers:customer_detail' item.order.customer.id %}" class="font-weight-bold text-info" title="بطاقة العميل">
                                        {{ item.order.customer.name }}
                                    </a>
                                    {% if item.order.total_amount and item.order.paid_amount and item.order.remaining_amount > 0 %}
                                        <br><small class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            مديونية: {{ item.order.remaining_amount|floatformat:2 }} ج.م
                                        </small>
                                    {% endif %}
                                </td>
                                <td>{{ item.order.customer.phone }}</td>
                                <td>{{ item.order.customer.address|default:"غير محدد" }}</td>
                                <td>
                                    {% if item.installation and item.installation.location_type == 'open' %}
                                        <span class="badge badge-success">مفتوح</span>
                                    {% elif item.installation and item.installation.location_type == 'compound' %}
                                        <span class="badge badge-info">كومبوند</span>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.order.salesperson.name|default:"غير محدد" }}</td>
                                <td>
                                    {{ item.order.contract_number|default:"غير محدد" }}
                                    {% if item.order.contract_number_2 %}<br><span class="text-muted">{{ item.order.contract_number_2 }}</span>{% endif %}
                                    {% if item.order.contract_number_3 %}<br><span class="text-muted">{{ item.order.contract_number_3 }}</span>{% endif %}
                                </td>
                                <td>
                                    {{ item.order.invoice_number|default:"غير محدد" }}
                                    {% if item.order.invoice_number_2 %}<br><span class="text-muted">{{ item.order.invoice_number_2 }}</span>{% endif %}
                                    {% if item.order.invoice_number_3 %}<br><span class="text-muted">{{ item.order.invoice_number_3 }}</span>{% endif %}
                                </td>
                                <td>{{ item.order.branch.name|default:"غير محدد" }}</td>
                                <td>{{ item.team.name|default:"غير محدد" }}</td>
                                <td>
                                    {% if item.scheduled_date %}
                                        {{ item.scheduled_date|date:"Y-m-d" }}
                                        {% if item.installation and item.installation.scheduled_time %}
                                            <br><small class="text-muted">{{ item.installation.scheduled_time|time:"H:i" }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.installation and item.installation.scheduled_time %}
                                        {{ item.installation.scheduled_time|time:"H:i" }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if item.order.total_amount and item.order.paid_amount %}
                                        {% if item.order.remaining_amount > 0 %}
                                            <span class="badge badge-warning" style="cursor: pointer;" 
                                                  onclick="showDebtWarning('{{ item.order.customer.name|escapejs }}', '{{ item.order.remaining_amount }}', '{{ item.order.id }}')">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                {{ item.order.remaining_amount|floatformat:2 }} ج.م
                                            </span>
                                        {% else %}
                                            <span class="badge badge-success">
                                                <i class="fas fa-check-circle"></i>
                                                مدفوع بالكامل
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if item.status == 'needs_scheduling' %}
                                        <span class="badge badge-warning">
                                            <i class="fas fa-calendar-plus"></i> بحاجة جدولة
                                        </span>
                                    {% elif item.status == 'under_manufacturing' %}
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-industry"></i> تحت التصنيع
                                        </span>
                                        {% if item.manufacturing_status %}
                                            <br><small class="text-muted">
                                                {% if item.manufacturing_status == 'pending_approval' %}قيد الموافقة
                                                {% elif item.manufacturing_status == 'approved' %}تمت الموافقة
                                                {% elif item.manufacturing_status == 'in_cutting' %}قيد القص
                                                {% elif item.manufacturing_status == 'cutting_completed' %}تم القص
                                                {% elif item.manufacturing_status == 'in_manufacturing' %}قيد التصنيع
                                                {% elif item.manufacturing_status == 'quality_check' %}فحص الجودة
                                                {% else %}{{ item.manufacturing_status }}{% endif %}
                                            </small>
                                        {% endif %}
                                    {% elif item.status == 'scheduled' %}
                                        <span class="badge badge-info">
                                            <i class="fas fa-calendar-check"></i> مجدول
                                        </span>
                                    {% elif item.status == 'in_installation' %}
                                        <span class="badge badge-primary">
                                            <i class="fas fa-tools"></i> قيد التركيب
                                        </span>
                                    {% elif item.status == 'completed' %}
                                        <span class="badge badge-success">
                                            <i class="fas fa-check-circle"></i> مكتمل
                                        </span>
                                    {% elif item.status == 'cancelled' %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-times-circle"></i> ملغي
                                        </span>
                                    {% elif item.status == 'modification_required' %}
                                        <span class="badge badge-warning">
                                            <i class="fas fa-exclamation-triangle"></i> يحتاج تعديل
                                        </span>
                                    {% elif item.status == 'modification_in_progress' %}
                                        <span class="badge badge-info">
                                            <i class="fas fa-cogs"></i> التعديل قيد التنفيذ
                                        </span>
                                    {% elif item.status == 'modification_completed' %}
                                        <span class="badge badge-success">
                                            <i class="fas fa-check-circle"></i> التعديل مكتمل
                                        </span>
                                    {% else %}
                                        <span class="badge badge-secondary">
                                            {% if item.installation %}{{ item.installation.get_status_display }}{% else %}{{ item.status }}{% endif %}
                                        </span>
                                    {% endif %}
                                    
                                    <!-- عرض حالة الطلب الأصلي -->
                                    {% if item.order.is_manufacturing_order %}
                                        {% if item.order.is_delivered_manufacturing_order %}
                                            <br><small class="text-info">
                                                <i class="fas fa-truck"></i> تم التسليم (أمر تصنيع)
                                            </small>
                                        {% else %}
                                            <br><small class="text-warning">
                                                <i class="fas fa-industry"></i> جاهز للتركيب (أمر تصنيع)
                                            </small>
                                        {% endif %}
                                    {% elif item.order.order_status == 'ready_install' or item.order.order_status == 'completed' %}
                                        <br><small class="text-success">
                                            <i class="fas fa-check-circle"></i> جاهز للتركيب
                                        </small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm action-buttons">
                                        {% if item.installation %}
                                            <a href="{% url 'installations:installation_detail' item.installation.pk %}" class="btn btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if perms.installations.change_installationschedule %}
                                                <a href="{% url 'installations:edit_schedule' item.installation.pk %}" class="btn btn-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            {% endif %}
                                            {% if perms.installations.delete_installationschedule %}
                                                <a href="{% url 'installations:installation_delete' item.installation.pk %}" class="btn btn-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            {% endif %}
                                        {% elif item.status == 'under_manufacturing' %}
                                            <!-- الطلبات تحت التصنيع - للعرض فقط -->
                                            <a href="{% url 'orders:order_detail' item.order.id %}" class="btn btn-info" title="تفاصيل الطلب">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <span class="badge badge-secondary" title="لا يمكن جدولة طلب تحت التصنيع">
                                                <i class="fas fa-lock"></i> تحت التصنيع
                                            </span>
                                        {% else %}
                                            <!-- زر جدولة للطلبات غير المجدولة -->
                                            <button class="btn btn-success schedule-installation-btn"
                                                    data-order-id="{{ item.order.id }}"
                                                    data-schedule-url="{% url 'installations:quick_schedule_installation' item.order.id %}"
                                                    title="جدولة تركيب">
                                                <i class="fas fa-calendar-plus"></i>
                                            </button>
                                            <a href="{% url 'orders:order_detail' item.order.id %}" class="btn btn-info" title="تفاصيل الطلب">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- ترقيم الصفحات -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-4x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">لا توجد تركيبات</h5>
                    <p class="text-gray-400">لم يتم العثور على تركيبات تطابق معايير البحث</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- نظام التحقق من المديونية - خاص بقسم التركيبات فقط -->
<script src="{% static 'js/debt_check.js' %}"></script>
<script>
// تحديث حالة التركيب مع SweetAlert2
document.querySelectorAll('.update-status').forEach(button => {
    button.addEventListener('click', function() {
        const installationId = this.dataset.installationId;
        const newStatus = this.dataset.status;
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

        Swal.fire({
            title: 'تأكيد العملية',
            text: 'هل أنت متأكد من تحديث حالة التركيب؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، تحديث',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/installations/installation/${installationId}/update-status/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=${newStatus}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'تم التحديث!',
                            text: 'تم تحديث حالة التركيب بنجاح',
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', 'حدث خطأ أثناء تحديث الحالة', 'error');
                    }
                });
            }
        });
    });
});

// تأكيد جدولة التركيب - تم نقل هذا النظام إلى debt_check.js
// الآن يتم التحقق من المديونية قبل الجدولة

// إضافة نظام التحقق من المديونية للأزرار الجديدة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة التحقق من المديونية لأزرار الجدولة
    if (typeof checkDebtBeforeScheduling === 'function') {
        // البحث عن جميع أزرار الجدولة وإضافة التحقق لها
        document.querySelectorAll('[data-schedule-url]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const orderId = this.getAttribute('data-order-id');
                const scheduleUrl = this.getAttribute('data-schedule-url');
                
                if (orderId && scheduleUrl) {
                    checkDebtBeforeScheduling(orderId, scheduleUrl);
                } else {
                    console.error('مفقود: order-id أو schedule-url');
                }
            });
        });
    } else {
        console.warn('checkDebtBeforeScheduling function not found. Make sure debt_check.js is loaded.');
    }
});

// البحث التلقائي مع تأخير
let searchTimeout;
const searchInput = document.querySelector('input[name="search"]');
if (searchInput) {
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.length >= 3 || this.value.length === 0) {
                const form = this.closest('form');
                form.submit();
            }
        }, 1000);
    });
}

// حفظ حالة الفلاتر في localStorage
function saveFilterState() {
    const formData = new FormData(document.getElementById('filterForm'));
    const filters = Object.fromEntries(formData);
    localStorage.setItem('installation_filters', JSON.stringify(filters));
}

function loadFilterState() {
    const saved = localStorage.getItem('installation_filters');
    if (saved && !window.location.search) {
        const filters = JSON.parse(saved);
        const form = document.getElementById('filterForm');
        Object.entries(filters).forEach(([key, value]) => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input && value) {
                input.value = value;
            }
        });
    }
}

// تحميل الفلاتر المحفوظة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadFilterState);

// حفظ الفلاتر عند التغيير
document.getElementById('filterForm').addEventListener('submit', saveFilterState);

// إضافة تأثيرات بصرية للجدول
document.querySelectorAll('tbody tr').forEach(row => {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#f8f9fa';
        this.style.transform = 'scale(1.01)';
        this.style.transition = 'all 0.2s ease';
    });
    
    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
        this.style.transform = '';
    });
});

// تحديث الإحصائيات في الوقت الفعلي
function updateStats() {
    fetch('{% url "installations:installation_list" %}' + window.location.search)
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newStats = doc.querySelector('.filter-stats');
            if (newStats) {
                document.querySelector('.filter-stats').innerHTML = newStats.innerHTML;
            }
        })
        .catch(console.error);
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(updateStats, 30000);

// إضافة مؤشرات تحميل
function showLoading() {
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin fa-3x text-primary"></i>
            <p class="mt-2">جاري التحميل...</p>
        </div>
    `;
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// إظهار مؤشر التحميل عند إرسال النماذج
document.getElementById('filterForm').addEventListener('submit', showLoading);

// إخفاء مؤشر التحميل عند اكتمال التحميل
window.addEventListener('load', hideLoading);

// وظيفة إظهار تحذير المديونية
function showDebtWarning(customerName, remainingAmount, orderId) {
    Swal.fire({
        title: '<i class="fas fa-exclamation-triangle text-warning"></i> تحذير مديونية',
        html: `
            <div class="text-right" dir="rtl">
                <p><strong>العميل:</strong> ${customerName}</p>
                <p><strong>المبلغ المتبقي:</strong> <span class="text-danger">${remainingAmount} ج.م</span></p>
                <hr>
                <p class="text-warning">
                    <i class="fas fa-info-circle"></i>
                    يوجد مديونية متبقية على هذا العميل. يرجى إقفال المديونية قبل التركيب.
                </p>
                <p class="text-muted">اختر أحد الخيارات التالية:</p>
            </div>
        `,
        showCancelButton: true,
        showDenyButton: true,
        confirmButtonText: '<i class="fas fa-money-bill-wave"></i> دفع المديونية',
        denyButtonText: '<i class="fas fa-clock"></i> استلام بعد التركيب',
        cancelButtonText: '<i class="fas fa-times"></i> إلغاء',
        confirmButtonColor: '#28a745',
        denyButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        width: '500px',
        padding: '2rem',
        backdrop: true,
        allowOutsideClick: false,
        customClass: {
            title: 'text-right',
            content: 'text-right'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إعادة توجيه لصفحة دفع المديونية
            window.open(`/orders/order/${orderId}/`, '_blank');
        } else if (result.isDenied) {
            // تسجيل ملاحظة استلام بعد التركيب
            Swal.fire({
                title: 'تم التأكيد',
                text: 'سيتم استلام المبلغ المتبقي بعد التركيب',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });
}
</script>
{% endblock %} 

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
/* تطبيق نفس الهوية البصرية لقسم المعاينات */

/* تحسين البطاقات الحديث */
.card { 
    border-radius: 0.5rem; 
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
    transition: all 0.3s ease;
} 

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* تحسين مظهر البادجات */
.badge { 
    font-size: 0.75rem; 
    font-weight: 600; 
    border-radius: 0.375rem; 
    padding: 0.375rem 0.5rem; 
    transition: all 0.2s ease;
} 

.badge:hover {
    transform: scale(1.05);
}

.badge-success { 
    background-color: #28a745 !important;
    color: white !important;
} 

.badge-info { 
    background-color: #17a2b8 !important;
    color: white !important;
} 

.badge-primary { 
    background-color: #007bff !important;
    color: white !important;
} 

.badge-warning { 
    background-color: #ffc107 !important;
    color: #212529 !important;
} 

.badge-danger { 
    background-color: #dc3545 !important;
    color: white !important;
} 

.badge-secondary { 
    background-color: #6c757d !important;
    color: white !important;
} 

/* تحسين مظهر نماذج الفلاتر */
.filter-card {
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.filter-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.5rem 0.5rem 0 0;
}

.filter-card .form-label { 
    font-weight: 600; 
    color: #495057; 
    margin-bottom: 0.25rem; 
} 

.filter-card .form-select, .filter-card .form-control { 
    font-size: 0.875rem; 
    padding: 0.375rem 0.75rem; 
    border-radius: 0.375rem; 
    border: 1px solid #ced4da;
    transition: all 0.15s ease-in-out;
} 

.filter-card .form-select:focus, .filter-card .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-card .btn { 
    font-size: 0.875rem; 
    padding: 0.375rem 0.75rem; 
    border-radius: 0.375rem; 
    font-weight: 500;
    transition: all 0.15s ease-in-out;
} 

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* تحسين مظهر الجدول */
.installations-table {
    margin-bottom: 0;
    border-radius: 0 0 0.5rem 0.5rem;
    overflow: hidden;
}

.installations-table th, .installations-table td {
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
}

.installations-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border: none;
    font-size: 0.875rem;
}

.installations-table tbody tr {
    transition: all 0.2s ease;
}

.installations-table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
    transform: scale(1.01);
}

.installations-table td {
    font-size: 0.875rem;
}

/* تحسين الروابط */
.installations-table a {
    transition: all 0.2s ease;
    font-weight: 600;
}

.installations-table a:hover {
    text-decoration: underline !important;
    opacity: 0.8;
}

.installations-table .text-primary {
    color: #007bff !important;
}

.installations-table .text-primary:hover {
    color: #0056b3 !important;
}

.installations-table .text-info {
    color: #17a2b8 !important;
}

.installations-table .text-info:hover {
    color: #117a8b !important;
}

/* تحسين مظهر أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    margin: 0;
}

.action-buttons .btn i {
    font-size: 0.75em;
}

/* تحسين مظهر ترقيم الصفحات */
.pagination {
    justify-content: center;
    margin-top: 1.5rem;
}

.pagination .page-link {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #007bff;
    padding: 0.5rem 0.75rem;
    transition: all 0.15s ease-in-out;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #0056b3;
    transform: translateY(-1px);
}

/* تحسين النصوص العربية */
body, .installations-table, .form-control, .btn {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تحسين الألوان والمساحات */
.text-muted {
    color: #6c757d !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* تحسين التنبيهات */
.alert {
    border-radius: 0.5rem;
    border: none;
    padding: 1rem 1.25rem;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* تحسين عرض بادجات المديونية */
.badge[onclick] {
    cursor: pointer;
    transition: all 0.3s ease;
}

.badge[onclick]:hover {
    transform: scale(1.1);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

/* توافق مع الأجهزة المحمولة */
@media (max-width: 768px) {
    .installations-table {
        font-size: 0.75rem;
    }
    
    .action-buttons .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.625rem;
    }
    
    .action-buttons .btn i {
        font-size: 0.625em;
    }
    
    .filter-stats .badge {
        font-size: 0.625rem;
        margin: 0.125rem;
    }
    
    .installations-table th, .installations-table td {
        padding: 0.5rem 0.25rem !important;
    }
}

/* تحسين شكل الفلاتر المطبقة */
.applied-filters .badge {
    margin-bottom: 0.25rem;
    margin-right: 0.25rem;
}

.applied-filters .badge a {
    text-decoration: none;
    font-weight: bold;
    margin-left: 0.25rem;
}

.applied-filters .badge a:hover {
    opacity: 0.7;
}

/* تحسين شكل عداد النتائج */
.filter-stats .badge {
    margin-left: 0.25rem;
    margin-right: 0.25rem;
}

/* تحسين شكل عنوان الصفحة */
.h3 {
    color: #495057;
    font-weight: 600;
}

.fas.fa-list {
    margin-left: 0.5rem;
}

/* تحسين شكل زر العودة */
.btn-outline-secondary {
    transition: all 0.15s ease-in-out;
}

.btn-outline-secondary:hover {
    transform: translateY(-1px);
}
</style>
{% endblock %}